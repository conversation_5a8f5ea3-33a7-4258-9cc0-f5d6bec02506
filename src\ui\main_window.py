# -*- coding: utf-8 -*-
"""
主窗口 - 应用程序的主界面
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                            QSplitter, QToolBar, QStatusBar, QPushButton,
                            QLineEdit, QLabel, QFrame, QMenu, QMessageBox)
from PyQt6.QtCore import Qt, QSize, QTimer
from PyQt6.QtGui import QIcon, QAction, QKeySequence
from pathlib import Path
import logging

from .category_list import CategoryList
from .file_view import FileView
from .preview_panel import PreviewPanel
from .mini_window import MiniWindow
from .styles import style_manager
from models.database import DatabaseManager
from utils.config_manager import ConfigManager
from utils.navigation import NavigationManager
from utils.search_sort import SearchSortManager, SortBy, SortOrder
from utils.performance import (PerformanceProfiler, MemoryMonitor,
                              performance_timer, cache_manager)
from utils.animations import FadeAnimation, animate_widget_show
from utils.theme_manager import ThemeManager


class MainWindow(QMainWindow):
    """主窗口类"""

    @performance_timer
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.db_manager = DatabaseManager(str(config_manager.config_dir / "metadata.db"))
        self.navigation_manager = NavigationManager(config_manager)
        self.search_sort_manager = SearchSortManager(config_manager, self.db_manager)
        self.mini_window = None

        # 性能监控
        self.profiler = PerformanceProfiler()
        self.memory_monitor = MemoryMonitor()
        self.memory_monitor.memory_warning.connect(self.on_memory_warning)

        # 主题管理器
        try:
            self.theme_manager = ThemeManager(config_manager)
        except Exception as e:
            logging.error(f"主题管理器初始化失败: {e}")
            self.theme_manager = None

        # 启动性能分析（开发模式）
        if config_manager.settings.get("debug_mode", False):
            self.profiler.start_profiling()
            self.memory_monitor.start_monitoring()

        self.init_ui()
        self.setup_shortcuts()
        self.restore_window_state()
        self.setup_navigation()

        # 安全地应用主题
        try:
            self.apply_theme()
        except Exception as e:
            logging.error(f"主题应用失败: {e}")
            # 使用基本样式
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
            """)

        # 如果是首次运行，显示欢迎信息
        if self.config_manager.is_first_run():
            self.show_welcome_message()

        # 延迟加载动画效果
        try:
            QTimer.singleShot(100, self.show_with_animation)
        except Exception as e:
            logging.error(f"动画效果加载失败: {e}")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("简笔画素材管理")
        self.setMinimumSize(800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧分类列表
        self.category_list = CategoryList(self.config_manager, self.db_manager)
        splitter.addWidget(self.category_list)
        
        # 创建中间文件视图
        self.file_view = FileView(self.config_manager, self.db_manager)
        splitter.addWidget(self.file_view)
        
        # 创建右侧预览面板
        self.preview_panel = PreviewPanel(self.config_manager)
        splitter.addWidget(self.preview_panel)
        
        # 设置分割器比例
        splitter.setSizes([200, 600, 300])
        
        # 连接信号
        self.category_list.category_changed.connect(self.on_category_changed)
        self.file_view.file_selected.connect(self.preview_panel.show_file_preview)
        
        # 创建工具栏和状态栏
        self.create_toolbar()
        self.create_statusbar()
        
        # 应用样式
        self.apply_styles()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar("主工具栏")
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        self.addToolBar(toolbar)
        
        # 导航按钮
        self.back_action = QAction("←", self)
        self.back_action.setToolTip("返回上一级")
        self.back_action.setEnabled(False)
        self.back_action.triggered.connect(self.go_back)
        toolbar.addAction(self.back_action)

        self.forward_action = QAction("→", self)
        self.forward_action.setToolTip("前进到下一级")
        self.forward_action.setEnabled(False)
        self.forward_action.triggered.connect(self.go_forward)
        toolbar.addAction(self.forward_action)
        
        toolbar.addSeparator()
        
        # 导入按钮
        import_action = QAction("导入", self)
        import_action.setToolTip("导入文件")
        import_action.triggered.connect(self.import_files)
        toolbar.addAction(import_action)
        
        toolbar.addSeparator()
        
        # 搜索框
        search_label = QLabel("搜索:")
        toolbar.addWidget(search_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入搜索关键词...")
        self.search_edit.setMaximumWidth(200)
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        toolbar.addWidget(self.search_edit)
        
        toolbar.addSeparator()
        
        # 导出按钮
        export_action = QAction("导出", self)
        export_action.setToolTip("导出选中文件")
        export_action.triggered.connect(self.export_files)
        toolbar.addAction(export_action)
        
        # 排序按钮
        self.sort_action = QAction("排序", self)
        self.sort_action.setToolTip("排序选项")
        self.sort_action.triggered.connect(self.show_sort_menu)
        toolbar.addAction(self.sort_action)
        
        # 设置按钮
        settings_action = QAction("设置", self)
        settings_action.setToolTip("应用设置")
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
        
        # 小窗模式按钮
        mini_action = QAction("小窗模式", self)
        mini_action.setToolTip("切换到小窗模式")
        mini_action.triggered.connect(self.toggle_mini_window)
        toolbar.addAction(mini_action)
    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        
        # 显示就绪状态
        self.statusbar.showMessage("就绪")
    
    def setup_shortcuts(self):
        """设置快捷键"""
        # F2 - 重命名
        rename_shortcut = QKeySequence("F2")
        rename_action = QAction(self)
        rename_action.setShortcut(rename_shortcut)
        rename_action.triggered.connect(self.rename_selected)
        self.addAction(rename_action)
        
        # Ctrl+F - 搜索
        search_shortcut = QKeySequence("Ctrl+F")
        search_action = QAction(self)
        search_action.setShortcut(search_shortcut)
        search_action.triggered.connect(self.focus_search)
        self.addAction(search_action)
        
        # Ctrl+A - 全选
        select_all_shortcut = QKeySequence("Ctrl+A")
        select_all_action = QAction(self)
        select_all_action.setShortcut(select_all_shortcut)
        select_all_action.triggered.connect(self.select_all)
        self.addAction(select_all_action)
        
        # Alt+Space+X - 小窗模式
        mini_shortcut = QKeySequence("Alt+Space, X")
        mini_action = QAction(self)
        mini_action.setShortcut(mini_shortcut)
        mini_action.triggered.connect(self.toggle_mini_window)
        self.addAction(mini_action)

        # Alt+Left - 后退
        back_shortcut = QKeySequence("Alt+Left")
        back_action = QAction(self)
        back_action.setShortcut(back_shortcut)
        back_action.triggered.connect(self.go_back)
        self.addAction(back_action)

        # Alt+Right - 前进
        forward_shortcut = QKeySequence("Alt+Right")
        forward_action = QAction(self)
        forward_action.setShortcut(forward_shortcut)
        forward_action.triggered.connect(self.go_forward)
        self.addAction(forward_action)

        # Backspace - 返回上一级
        up_shortcut = QKeySequence("Backspace")
        up_action = QAction(self)
        up_action.setShortcut(up_shortcut)
        up_action.triggered.connect(self.go_up_level)
        self.addAction(up_action)
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QToolBar {
            background-color: #3c3c3c;
            border: none;
            spacing: 5px;
            padding: 5px;
        }
        
        QToolBar QToolButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px 10px;
            color: #ffffff;
        }
        
        QToolBar QToolButton:hover {
            background-color: #5a5a5a;
        }
        
        QToolBar QToolButton:pressed {
            background-color: #6a6a6a;
        }
        
        QLineEdit {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QStatusBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-top: 1px solid #5a5a5a;
        }
        """
        self.setStyleSheet(style)
    
    def restore_window_state(self):
        """恢复窗口状态"""
        pos = self.config_manager.settings["window_position"]
        self.setGeometry(pos["x"], pos["y"], pos["width"], pos["height"])
    
    def show_welcome_message(self):
        """显示欢迎信息"""
        self.statusbar.showMessage("欢迎使用简笔画素材管理软件！")
        self.config_manager.set_first_run_complete()

    def show_with_animation(self):
        """带动画效果显示窗口"""
        animate_widget_show(self, "fade")

    def apply_theme(self):
        """应用主题"""
        try:
            current_theme = self.config_manager.settings.get("theme", "dark")
            stylesheet = style_manager.get_stylesheet(current_theme)
            self.setStyleSheet(stylesheet)

            # 应用主题管理器的主题
            if self.theme_manager:
                self.theme_manager.apply_theme(self, current_theme)
        except Exception as e:
            logging.error(f"主题应用失败: {e}")
            # 使用基本的暗色主题
            basic_style = """
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QWidget {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 6px 12px;
                    color: #ffffff;
                }
                QPushButton:hover {
                    background-color: #4a4a4a;
                }
                QLineEdit {
                    background-color: #404040;
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 6px;
                    color: #ffffff;
                }
            """
            self.setStyleSheet(basic_style)

    def on_memory_warning(self, memory_mb: int):
        """内存警告处理"""
        logging.warning(f"内存使用警告：{memory_mb}MB")

        # 清理缓存
        cache_manager.clear()

        # 显示警告消息
        QMessageBox.warning(
            self,
            "内存警告",
            f"当前内存使用过高：{memory_mb}MB\n"
            "已自动清理缓存以释放内存。"
        )
    
    # 槽函数
    def import_files(self):
        """导入文件"""
        self.statusbar.showMessage("导入功能开发中...")
    
    def export_files(self):
        """导出文件"""
        self.statusbar.showMessage("导出功能开发中...")
    
    def show_sort_menu(self):
        """显示排序菜单"""
        menu = QMenu(self)

        # 获取当前排序设置
        current_sort_by, current_sort_order = self.search_sort_manager.get_sort_preferences()

        # 排序方式选项
        sort_by_group = []

        name_action = menu.addAction("按名称排序")
        name_action.setCheckable(True)
        name_action.setChecked(current_sort_by == SortBy.NAME)
        name_action.triggered.connect(lambda: self.set_sort_by(SortBy.NAME))
        sort_by_group.append(name_action)

        type_action = menu.addAction("按类型排序")
        type_action.setCheckable(True)
        type_action.setChecked(current_sort_by == SortBy.TYPE)
        type_action.triggered.connect(lambda: self.set_sort_by(SortBy.TYPE))
        sort_by_group.append(type_action)

        size_action = menu.addAction("按大小排序")
        size_action.setCheckable(True)
        size_action.setChecked(current_sort_by == SortBy.SIZE)
        size_action.triggered.connect(lambda: self.set_sort_by(SortBy.SIZE))
        sort_by_group.append(size_action)

        time_action = menu.addAction("按修改时间排序")
        time_action.setCheckable(True)
        time_action.setChecked(current_sort_by == SortBy.MODIFIED_TIME)
        time_action.triggered.connect(lambda: self.set_sort_by(SortBy.MODIFIED_TIME))
        sort_by_group.append(time_action)

        menu.addSeparator()

        # 排序顺序选项
        asc_action = menu.addAction("升序")
        asc_action.setCheckable(True)
        asc_action.setChecked(current_sort_order == SortOrder.ASCENDING)
        asc_action.triggered.connect(lambda: self.set_sort_order(SortOrder.ASCENDING))

        desc_action = menu.addAction("降序")
        desc_action.setCheckable(True)
        desc_action.setChecked(current_sort_order == SortOrder.DESCENDING)
        desc_action.triggered.connect(lambda: self.set_sort_order(SortOrder.DESCENDING))

        # 显示菜单
        menu.exec()
    
    def show_settings(self):
        """显示设置对话框"""
        self.statusbar.showMessage("设置功能开发中...")
    
    def toggle_mini_window(self):
        """切换小窗模式"""
        if self.mini_window is None:
            self.mini_window = MiniWindow(self.config_manager, self.db_manager)
        
        if self.mini_window.isVisible():
            self.mini_window.hide()
            self.show()
        else:
            self.hide()
            self.mini_window.show()
    
    def on_search_text_changed(self, text):
        """搜索文本改变"""
        if text.strip():
            # 使用搜索排序管理器进行搜索
            current_category = self.category_list.get_current_category()
            search_results = self.search_sort_manager.search_files(text, current_category)
            self.file_view.display_search_results(search_results)
        else:
            self.file_view.refresh_current_view()
    
    def rename_selected(self):
        """重命名选中项"""
        self.file_view.rename_selected()
    
    def focus_search(self):
        """聚焦搜索框"""
        self.search_edit.setFocus()
        self.search_edit.selectAll()
    
    def select_all(self):
        """全选"""
        self.file_view.select_all()

    def setup_navigation(self):
        """设置导航功能"""
        self.update_navigation_buttons()

    def on_category_changed(self, category: str):
        """分类改变事件"""
        # 添加到导航历史
        self.navigation_manager.add_location(category)

        # 切换文件视图
        self.file_view.set_category(category)

        # 更新导航按钮状态
        self.update_navigation_buttons()

    def go_back(self):
        """后退导航"""
        location = self.navigation_manager.go_back()
        if location:
            category = location.get("category", "")
            subfolder = location.get("subfolder", "")

            # 更新分类选择（不触发信号）
            self.category_list.set_current_category(category, emit_signal=False)

            # 切换文件视图
            self.file_view.set_category(category, subfolder)

            # 更新导航按钮状态
            self.update_navigation_buttons()

    def go_forward(self):
        """前进导航"""
        location = self.navigation_manager.go_forward()
        if location:
            category = location.get("category", "")
            subfolder = location.get("subfolder", "")

            # 更新分类选择（不触发信号）
            self.category_list.set_current_category(category, emit_signal=False)

            # 切换文件视图
            self.file_view.set_category(category, subfolder)

            # 更新导航按钮状态
            self.update_navigation_buttons()

    def update_navigation_buttons(self):
        """更新导航按钮状态"""
        self.back_action.setEnabled(self.navigation_manager.can_go_back())
        self.forward_action.setEnabled(self.navigation_manager.can_go_forward())

    def set_sort_by(self, sort_by: SortBy):
        """设置排序方式"""
        _, current_sort_order = self.search_sort_manager.get_sort_preferences()
        self.search_sort_manager.set_sort_preferences(sort_by, current_sort_order)
        self.file_view.refresh_current_view()
        self.statusbar.showMessage(f"排序方式已更改为: {sort_by.value}")

    def set_sort_order(self, sort_order: SortOrder):
        """设置排序顺序"""
        current_sort_by, _ = self.search_sort_manager.get_sort_preferences()
        self.search_sort_manager.set_sort_preferences(current_sort_by, sort_order)
        self.file_view.refresh_current_view()
        self.statusbar.showMessage(f"排序顺序已更改为: {'升序' if sort_order == SortOrder.ASCENDING else '降序'}")

    def go_up_level(self):
        """返回上一级目录"""
        current_subfolder = self.file_view.current_subfolder
        if current_subfolder:
            # 如果在子文件夹中，返回上一级
            parent_folder = str(Path(current_subfolder).parent)
            if parent_folder == ".":
                parent_folder = ""

            self.file_view.set_category(self.file_view.current_category, parent_folder)

            # 添加到导航历史
            self.navigation_manager.add_location(
                self.file_view.current_category,
                parent_folder
            )
            self.update_navigation_buttons()

    def closeEvent(self, event):
        """关闭事件"""
        # 停止性能监控
        if self.config_manager.settings.get("debug_mode", False):
            if self.profiler.is_profiling:
                stats = self.profiler.stop_profiling()
                if stats:
                    logging.info("性能分析结果：\n" + stats)

            self.memory_monitor.stop_monitoring()

        # 保存窗口状态
        geometry = self.geometry()
        self.config_manager.settings["window_position"] = {
            "x": geometry.x(),
            "y": geometry.y(),
            "width": geometry.width(),
            "height": geometry.height()
        }
        self.config_manager.save_settings()

        # 关闭小窗口
        if self.mini_window:
            self.mini_window.close()

        event.accept()
