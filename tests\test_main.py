# -*- coding: utf-8 -*-
"""
主要功能测试
"""

import sys
import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt

from utils.config_manager import ConfigManager
from models.database import DatabaseManager
from ui.main_window import MainWindow


class TestMainWindow(unittest.TestCase):
    """主窗口测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(config_dir=Path(self.temp_dir))
        self.main_window = MainWindow(self.config_manager)
    
    def tearDown(self):
        """清理测试"""
        self.main_window.close()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_window_initialization(self):
        """测试窗口初始化"""
        self.assertIsNotNone(self.main_window)
        self.assertEqual(self.main_window.windowTitle(), "简笔画素材管理")
        self.assertGreaterEqual(self.main_window.width(), 800)
        self.assertGreaterEqual(self.main_window.height(), 600)
    
    def test_components_creation(self):
        """测试组件创建"""
        self.assertIsNotNone(self.main_window.category_list)
        self.assertIsNotNone(self.main_window.file_view)
        self.assertIsNotNone(self.main_window.preview_panel)
        self.assertIsNotNone(self.main_window.db_manager)
        self.assertIsNotNone(self.main_window.navigation_manager)
    
    def test_toolbar_creation(self):
        """测试工具栏创建"""
        toolbar = self.main_window.toolbar
        self.assertIsNotNone(toolbar)
        
        # 检查工具栏按钮
        actions = toolbar.actions()
        self.assertGreater(len(actions), 0)
    
    def test_menu_creation(self):
        """测试菜单创建"""
        menubar = self.main_window.menuBar()
        self.assertIsNotNone(menubar)
        
        # 检查菜单项
        menus = menubar.actions()
        self.assertGreater(len(menus), 0)
    
    def test_shortcuts(self):
        """测试快捷键"""
        # 这里可以测试快捷键是否正确设置
        pass
    
    def test_mini_window_toggle(self):
        """测试小窗模式切换"""
        # 初始状态应该没有小窗
        self.assertIsNone(self.main_window.mini_window)
        
        # 切换到小窗模式
        self.main_window.toggle_mini_window()
        self.assertIsNotNone(self.main_window.mini_window)
        
        # 再次切换应该关闭小窗
        self.main_window.toggle_mini_window()
        # 小窗应该被隐藏但对象可能仍存在


class TestConfigManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        """设置测试"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(config_dir=Path(self.temp_dir))
    
    def tearDown(self):
        """清理测试"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_initialization(self):
        """测试配置初始化"""
        self.assertIsNotNone(self.config_manager.settings)
        self.assertIn("storage_path", self.config_manager.settings)
        self.assertIn("window_position", self.config_manager.settings)
    
    def test_save_and_load_settings(self):
        """测试设置保存和加载"""
        # 修改设置
        self.config_manager.settings["test_key"] = "test_value"
        self.config_manager.save_settings()
        
        # 创建新的配置管理器实例
        new_config_manager = ConfigManager(config_dir=Path(self.temp_dir))
        self.assertEqual(new_config_manager.settings.get("test_key"), "test_value")
    
    def test_first_run_detection(self):
        """测试首次运行检测"""
        # 新的配置管理器应该检测到首次运行
        self.assertTrue(self.config_manager.is_first_run())
        
        # 设置首次运行完成
        self.config_manager.set_first_run_complete()
        self.assertFalse(self.config_manager.is_first_run())


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试"""
    
    def setUp(self):
        """设置测试"""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = Path(self.temp_dir) / "test.db"
        self.db_manager = DatabaseManager(str(self.db_path))
    
    def tearDown(self):
        """清理测试"""
        self.db_manager.close()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_database_creation(self):
        """测试数据库创建"""
        self.assertTrue(self.db_path.exists())
    
    def test_table_creation(self):
        """测试表创建"""
        # 检查表是否存在
        cursor = self.db_manager.connection.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        self.assertIn("files", tables)
        self.assertIn("folders", tables)
    
    def test_file_operations(self):
        """测试文件操作"""
        # 添加文件
        file_data = {
            "name": "test.jpg",
            "path": "/test/test.jpg",
            "category": "人物",
            "type": "image"
        }
        
        file_id = self.db_manager.add_file(**file_data)
        self.assertIsNotNone(file_id)
        
        # 获取文件
        file_info = self.db_manager.get_file(file_id)
        self.assertIsNotNone(file_info)
        self.assertEqual(file_info["name"], "test.jpg")
        
        # 更新文件
        self.db_manager.update_file(file_id, name="updated.jpg")
        updated_file = self.db_manager.get_file(file_id)
        self.assertEqual(updated_file["name"], "updated.jpg")
        
        # 删除文件
        self.db_manager.delete_file(file_id)
        deleted_file = self.db_manager.get_file(file_id)
        self.assertIsNone(deleted_file)
    
    def test_folder_operations(self):
        """测试文件夹操作"""
        # 添加文件夹
        folder_data = {
            "name": "test_folder",
            "path": "/test/test_folder",
            "category": "人物"
        }
        
        folder_id = self.db_manager.add_folder(**folder_data)
        self.assertIsNotNone(folder_id)
        
        # 获取文件夹
        folder_info = self.db_manager.get_folder(folder_id)
        self.assertIsNotNone(folder_info)
        self.assertEqual(folder_info["name"], "test_folder")


class TestFileOperations(unittest.TestCase):
    """文件操作测试"""
    
    def setUp(self):
        """设置测试"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(config_dir=Path(self.temp_dir))
    
    def tearDown(self):
        """清理测试"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_file_import(self):
        """测试文件导入"""
        # 创建测试文件
        test_file = Path(self.temp_dir) / "test.txt"
        test_file.write_text("test content")
        
        # 这里需要实际的文件操作类来测试
        # 由于文件操作类可能比较复杂，这里只是示例
        pass
    
    def test_file_export(self):
        """测试文件导出"""
        pass
    
    def test_file_move(self):
        """测试文件移动"""
        pass


class TestSearchSort(unittest.TestCase):
    """搜索排序测试"""
    
    def setUp(self):
        """设置测试"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(config_dir=Path(self.temp_dir))
        self.db_manager = DatabaseManager(str(Path(self.temp_dir) / "test.db"))
    
    def tearDown(self):
        """清理测试"""
        self.db_manager.close()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_search_functionality(self):
        """测试搜索功能"""
        # 添加测试数据
        self.db_manager.add_file(
            name="test1.jpg",
            path="/test/test1.jpg",
            category="人物",
            type="image"
        )
        self.db_manager.add_file(
            name="example.png",
            path="/test/example.png",
            category="场景",
            type="image"
        )
        
        # 这里需要实际的搜索类来测试
        pass
    
    def test_sort_functionality(self):
        """测试排序功能"""
        pass


if __name__ == "__main__":
    # 运行测试
    unittest.main()
