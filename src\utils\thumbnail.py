# -*- coding: utf-8 -*-
"""
缩略图生成工具 - 为不同类型的文件生成缩略图
"""

import os
import hashlib
from pathlib import Path
from typing import Optional, Tuple
from PIL import Image, ImageDraw, ImageFont
from PyQt6.QtGui import QPixmap, QPainter, QColor, QFont
from PyQt6.QtCore import QSize, Qt, QThread, pyqtSignal, QRunnable, QObject
from PyQt6.QtWidgets import QApplication
import logging
import time
from concurrent.futures import ThreadPoolExecutor

from utils.config_manager import ConfigManager


class ThumbnailWorker(QRunnable):
    """缩略图生成工作线程"""

    def __init__(self, file_path: str, thumbnail_generator, callback=None):
        super().__init__()
        self.file_path = file_path
        self.thumbnail_generator = thumbnail_generator
        self.callback = callback

    def run(self):
        """运行缩略图生成"""
        try:
            thumbnail_path = self.thumbnail_generator._generate_thumbnail_sync(self.file_path)
            if self.callback:
                self.callback(self.file_path, thumbnail_path)
        except Exception as e:
            logging.error(f"生成缩略图失败 {self.file_path}: {e}")
            if self.callback:
                self.callback(self.file_path, None)


class ThumbnailGenerator:
    """缩略图生成器"""

    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.thumbnail_dir = config_manager.config_dir / "thumbnails"
        self.thumbnail_dir.mkdir(exist_ok=True)

        # 缩略图尺寸
        self.thumbnail_size = config_manager.settings.get("thumbnail_size", 100)

        # 支持的图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'}
        self.video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
        self.audio_extensions = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma'}
        self.text_extensions = {'.txt', '.md', '.py', '.js', '.html', '.css', '.json'}

        # 缓存
        self.cache = {}
        self.cache_size_limit = 1000

        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
    
    def generate_thumbnail_async(self, file_path: str, callback=None, force_regenerate: bool = False):
        """异步生成缩略图"""
        # 检查缓存
        cache_key = f"{file_path}_{self.thumbnail_size}"
        if not force_regenerate and cache_key in self.cache:
            if callback:
                callback(file_path, self.cache[cache_key])
            return

        # 提交到线程池
        worker = ThumbnailWorker(file_path, self, callback)
        self.thread_pool.submit(worker.run)

    def generate_thumbnail(self, file_path: str, force_regenerate: bool = False) -> Optional[str]:
        """
        生成文件缩略图
        
        Args:
            file_path: 文件路径
            force_regenerate: 是否强制重新生成
            
        Returns:
            str: 缩略图文件路径，失败返回None
        """
        # 检查缓存
        cache_key = f"{file_path}_{self.thumbnail_size}"
        if not force_regenerate and cache_key in self.cache:
            return self.cache[cache_key]

        return self._generate_thumbnail_sync(file_path, force_regenerate)

    def _generate_thumbnail_sync(self, file_path: str, force_regenerate: bool = False) -> Optional[str]:
        """同步生成缩略图"""
        if not os.path.exists(file_path):
            return None

        # 生成缩略图文件名（基于文件路径的哈希值）
        file_hash = self._get_file_hash(file_path)
        thumbnail_path = self.thumbnail_dir / f"{file_hash}.png"
        
        # 如果缩略图已存在且不强制重新生成，直接返回
        if thumbnail_path.exists() and not force_regenerate:
            result = str(thumbnail_path)
            self._update_cache(file_path, result)
            return result

        # 记录开始时间（性能分析）
        start_time = time.time()

        file_ext = Path(file_path).suffix.lower()

        try:
            if file_ext in self.image_extensions:
                result = self._generate_image_thumbnail(file_path, str(thumbnail_path))
            elif file_ext in self.video_extensions:
                result = self._generate_video_thumbnail(file_path, str(thumbnail_path))
            elif file_ext in self.audio_extensions:
                result = self._generate_audio_thumbnail(file_path, str(thumbnail_path))
            elif file_ext in self.text_extensions:
                result = self._generate_text_thumbnail(file_path, str(thumbnail_path))
            else:
                result = self._generate_default_thumbnail(file_path, str(thumbnail_path))

            # 更新缓存
            if result:
                self._update_cache(file_path, result)

            # 记录性能数据
            end_time = time.time()
            if end_time - start_time > 0.1:  # 只记录耗时超过100ms的操作
                logging.debug(f"缩略图生成耗时: {end_time - start_time:.3f}s - {file_path}")

            return result
        except Exception as e:
            logging.error(f"生成缩略图失败 {file_path}: {e}")
            result = self._generate_default_thumbnail(file_path, str(thumbnail_path))
            if result:
                self._update_cache(file_path, result)
            return result
    
    def _generate_image_thumbnail(self, file_path: str, thumbnail_path: str) -> str:
        """生成图片缩略图"""
        try:
            with Image.open(file_path) as img:
                # 转换为RGB模式（处理RGBA等格式）
                if img.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 生成缩略图
                img.thumbnail((self.thumbnail_size, self.thumbnail_size), Image.Resampling.LANCZOS)
                
                # 创建正方形画布
                thumbnail = Image.new('RGB', (self.thumbnail_size, self.thumbnail_size), (240, 240, 240))
                
                # 居中粘贴缩略图
                x = (self.thumbnail_size - img.width) // 2
                y = (self.thumbnail_size - img.height) // 2
                thumbnail.paste(img, (x, y))
                
                # 保存缩略图
                thumbnail.save(thumbnail_path, 'PNG')
                return thumbnail_path
        except Exception as e:
            print(f"生成图片缩略图失败: {e}")
            return self._generate_default_thumbnail(file_path, thumbnail_path)
    
    def _generate_video_thumbnail(self, file_path: str, thumbnail_path: str) -> str:
        """生成视频缩略图"""
        # 这里应该使用FFmpeg提取视频关键帧
        # 暂时生成一个视频图标缩略图
        return self._generate_icon_thumbnail("🎬", "视频", thumbnail_path, (100, 149, 237))
    
    def _generate_audio_thumbnail(self, file_path: str, thumbnail_path: str) -> str:
        """生成音频缩略图"""
        return self._generate_icon_thumbnail("🎵", "音频", thumbnail_path, (255, 140, 0))
    
    def _generate_text_thumbnail(self, file_path: str, thumbnail_path: str) -> str:
        """生成文本文件缩略图"""
        try:
            # 读取文件前几行内容
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(200)  # 读取前200个字符
            
            # 创建缩略图
            img = Image.new('RGB', (self.thumbnail_size, self.thumbnail_size), (255, 255, 255))
            draw = ImageDraw.Draw(img)
            
            # 绘制文档图标背景
            draw.rectangle([10, 10, self.thumbnail_size-10, self.thumbnail_size-10], 
                         fill=(248, 248, 248), outline=(200, 200, 200))
            
            # 绘制文本内容（简化版）
            try:
                # 尝试加载字体
                font = ImageFont.truetype("arial.ttf", 8)
            except:
                font = ImageFont.load_default()
            
            # 绘制文本预览
            lines = content.split('\n')[:8]  # 最多8行
            y = 15
            for line in lines:
                if y > self.thumbnail_size - 20:
                    break
                # 截断过长的行
                if len(line) > 15:
                    line = line[:15] + "..."
                draw.text((15, y), line, fill=(50, 50, 50), font=font)
                y += 10
            
            img.save(thumbnail_path, 'PNG')
            return thumbnail_path
            
        except Exception as e:
            print(f"生成文本缩略图失败: {e}")
            return self._generate_icon_thumbnail("📄", "文本", thumbnail_path, (70, 130, 180))
    
    def _generate_default_thumbnail(self, file_path: str, thumbnail_path: str) -> str:
        """生成默认缩略图"""
        file_ext = Path(file_path).suffix.upper().lstrip('.')
        if not file_ext:
            file_ext = "FILE"
        return self._generate_icon_thumbnail("📄", file_ext, thumbnail_path, (128, 128, 128))
    
    def _generate_icon_thumbnail(self, icon: str, text: str, thumbnail_path: str, 
                                color: Tuple[int, int, int]) -> str:
        """生成图标式缩略图"""
        try:
            img = Image.new('RGB', (self.thumbnail_size, self.thumbnail_size), (245, 245, 245))
            draw = ImageDraw.Draw(img)
            
            # 绘制背景圆角矩形
            margin = 8
            draw.rounded_rectangle(
                [margin, margin, self.thumbnail_size-margin, self.thumbnail_size-margin],
                radius=8, fill=color, outline=(200, 200, 200)
            )
            
            # 绘制图标（使用Unicode字符）
            try:
                # 尝试加载支持Unicode的字体
                icon_font = ImageFont.truetype("seguiemj.ttf", 32)  # Windows Emoji字体
            except:
                try:
                    icon_font = ImageFont.truetype("arial.ttf", 32)
                except:
                    icon_font = ImageFont.load_default()
            
            # 计算图标位置
            icon_bbox = draw.textbbox((0, 0), icon, font=icon_font)
            icon_width = icon_bbox[2] - icon_bbox[0]
            icon_height = icon_bbox[3] - icon_bbox[1]
            icon_x = (self.thumbnail_size - icon_width) // 2
            icon_y = (self.thumbnail_size - icon_height) // 2 - 10
            
            draw.text((icon_x, icon_y), icon, fill=(255, 255, 255), font=icon_font)
            
            # 绘制文本标签
            try:
                text_font = ImageFont.truetype("arial.ttf", 10)
            except:
                text_font = ImageFont.load_default()
            
            text_bbox = draw.textbbox((0, 0), text, font=text_font)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = (self.thumbnail_size - text_width) // 2
            text_y = self.thumbnail_size - 20
            
            draw.text((text_x, text_y), text, fill=(255, 255, 255), font=text_font)
            
            img.save(thumbnail_path, 'PNG')
            return thumbnail_path
            
        except Exception as e:
            print(f"生成图标缩略图失败: {e}")
            # 创建最简单的缩略图
            img = Image.new('RGB', (self.thumbnail_size, self.thumbnail_size), color)
            img.save(thumbnail_path, 'PNG')
            return thumbnail_path
    
    def _get_file_hash(self, file_path: str) -> str:
        """获取文件路径的哈希值"""
        return hashlib.md5(file_path.encode('utf-8')).hexdigest()
    
    def get_thumbnail_path(self, file_path: str) -> Optional[str]:
        """获取缩略图路径（不生成）"""
        file_hash = self._get_file_hash(file_path)
        thumbnail_path = self.thumbnail_dir / f"{file_hash}.png"
        return str(thumbnail_path) if thumbnail_path.exists() else None
    
    def clear_thumbnails(self):
        """清理所有缩略图"""
        try:
            for thumbnail_file in self.thumbnail_dir.glob("*.png"):
                thumbnail_file.unlink()
            print("缩略图缓存已清理")
            # 清理内存缓存
            self.cache.clear()
        except Exception as e:
            print(f"清理缩略图失败: {e}")

    def get_cache_size(self) -> int:
        """获取缩略图缓存大小（字节）"""
        total_size = 0
        try:
            for thumbnail_file in self.thumbnail_dir.glob("*.png"):
                total_size += thumbnail_file.stat().st_size
        except Exception:
            pass
        return total_size

    def _update_cache(self, file_path: str, thumbnail_path: str):
        """更新缓存"""
        cache_key = f"{file_path}_{self.thumbnail_size}"

        # 如果缓存已满，移除最旧的项
        if len(self.cache) >= self.cache_size_limit:
            # 简单的FIFO策略
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]

        self.cache[cache_key] = thumbnail_path

    def preload_thumbnails(self, file_paths: list, callback=None):
        """预加载缩略图"""
        for file_path in file_paths:
            self.generate_thumbnail_async(file_path, callback)

    def shutdown(self):
        """关闭线程池"""
        self.thread_pool.shutdown(wait=True)
