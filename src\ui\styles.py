# -*- coding: utf-8 -*-
"""
样式表模块 - 统一的UI样式定义
"""

from typing import Dict, Any


class StyleManager:
    """样式管理器"""
    
    def __init__(self):
        self.current_theme = "dark"
        self.themes = {
            "dark": self._get_dark_theme(),
            "light": self._get_light_theme(),
            "blue": self._get_blue_theme()
        }
    
    def get_stylesheet(self, theme: str = None) -> str:
        """获取样式表"""
        if theme is None:
            theme = self.current_theme
        
        if theme not in self.themes:
            theme = "dark"
        
        return self.themes[theme]
    
    def set_theme(self, theme: str):
        """设置主题"""
        if theme in self.themes:
            self.current_theme = theme
    
    def _get_dark_theme(self) -> str:
        """暗色主题"""
        return """
        /* 主窗口 */
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        /* 工具栏 */
        QToolBar {
            background-color: #3c3c3c;
            border: none;
            spacing: 3px;
            padding: 5px;
        }
        
        QToolBar::separator {
            background-color: #555555;
            width: 1px;
            margin: 5px;
        }
        
        /* 按钮 */
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 6px 12px;
            color: #ffffff;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #4a4a4a;
            border-color: #666666;
        }
        
        QPushButton:pressed {
            background-color: #353535;
            border-color: #444444;
        }
        
        QPushButton:disabled {
            background-color: #2a2a2a;
            border-color: #333333;
            color: #666666;
        }
        
        /* 输入框 */
        QLineEdit {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 6px;
            color: #ffffff;
            selection-background-color: #0078d4;
        }
        
        QLineEdit:focus {
            border-color: #0078d4;
        }
        
        /* 列表控件 */
        QListWidget {
            background-color: #353535;
            border: 1px solid #555555;
            border-radius: 4px;
            color: #ffffff;
            outline: none;
        }
        
        QListWidget::item {
            padding: 8px;
            border-bottom: 1px solid #444444;
        }
        
        QListWidget::item:selected {
            background-color: #0078d4;
        }
        
        QListWidget::item:hover {
            background-color: #404040;
        }
        
        /* 滚动条 */
        QScrollBar:vertical {
            background-color: #2b2b2b;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #555555;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #666666;
        }
        
        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            height: 0px;
        }
        
        QScrollBar:horizontal {
            background-color: #2b2b2b;
            height: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:horizontal {
            background-color: #555555;
            border-radius: 6px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: #666666;
        }
        
        /* 分割器 */
        QSplitter::handle {
            background-color: #555555;
        }
        
        QSplitter::handle:horizontal {
            width: 2px;
        }
        
        QSplitter::handle:vertical {
            height: 2px;
        }
        
        /* 状态栏 */
        QStatusBar {
            background-color: #3c3c3c;
            border-top: 1px solid #555555;
            color: #ffffff;
        }
        
        /* 菜单 */
        QMenu {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
            padding: 4px;
        }
        
        QMenu::item {
            padding: 6px 20px;
            border-radius: 4px;
        }
        
        QMenu::item:selected {
            background-color: #0078d4;
        }
        
        QMenu::separator {
            height: 1px;
            background-color: #555555;
            margin: 4px 0px;
        }
        
        /* 标签页 */
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #353535;
        }
        
        QTabBar::tab {
            background-color: #404040;
            border: 1px solid #555555;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QTabBar::tab:selected {
            background-color: #0078d4;
        }
        
        QTabBar::tab:hover {
            background-color: #4a4a4a;
        }
        
        /* 进度条 */
        QProgressBar {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 4px;
            text-align: center;
            color: #ffffff;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 3px;
        }
        
        /* 复选框 */
        QCheckBox {
            color: #ffffff;
            spacing: 8px;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #555555;
            border-radius: 3px;
            background-color: #404040;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }
        
        /* 单选框 */
        QRadioButton {
            color: #ffffff;
            spacing: 8px;
        }
        
        QRadioButton::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #555555;
            border-radius: 8px;
            background-color: #404040;
        }
        
        QRadioButton::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }
        
        /* 组合框 */
        QComboBox {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 6px;
            color: #ffffff;
        }
        
        QComboBox:hover {
            border-color: #666666;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: url(down_arrow.png);
            width: 12px;
            height: 12px;
        }
        
        QComboBox QAbstractItemView {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
            selection-background-color: #0078d4;
        }
        """
    
    def _get_light_theme(self) -> str:
        """亮色主题"""
        return """
        /* 主窗口 */
        QMainWindow {
            background-color: #f0f0f0;
            color: #000000;
        }
        
        /* 工具栏 */
        QToolBar {
            background-color: #e0e0e0;
            border: none;
            spacing: 3px;
            padding: 5px;
        }
        
        /* 按钮 */
        QPushButton {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 6px 12px;
            color: #000000;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #f5f5f5;
            border-color: #999999;
        }
        
        QPushButton:pressed {
            background-color: #e0e0e0;
            border-color: #666666;
        }
        
        /* 输入框 */
        QLineEdit {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 6px;
            color: #000000;
            selection-background-color: #0078d4;
        }
        
        QLineEdit:focus {
            border-color: #0078d4;
        }
        
        /* 列表控件 */
        QListWidget {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            color: #000000;
            outline: none;
        }
        
        QListWidget::item {
            padding: 8px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        QListWidget::item:selected {
            background-color: #0078d4;
            color: #ffffff;
        }
        
        QListWidget::item:hover {
            background-color: #f0f0f0;
        }
        """
    
    def _get_blue_theme(self) -> str:
        """蓝色主题"""
        return """
        /* 主窗口 */
        QMainWindow {
            background-color: #1e3a5f;
            color: #ffffff;
        }
        
        /* 工具栏 */
        QToolBar {
            background-color: #2c5282;
            border: none;
            spacing: 3px;
            padding: 5px;
        }
        
        /* 按钮 */
        QPushButton {
            background-color: #3182ce;
            border: 1px solid #2c5282;
            border-radius: 4px;
            padding: 6px 12px;
            color: #ffffff;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #2c5282;
            border-color: #2a4365;
        }
        
        QPushButton:pressed {
            background-color: #2a4365;
            border-color: #1a365d;
        }
        """


# 全局样式管理器实例
style_manager = StyleManager()
