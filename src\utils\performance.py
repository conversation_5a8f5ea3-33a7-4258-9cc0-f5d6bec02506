# -*- coding: utf-8 -*-
"""
性能优化工具 - 提供性能分析和优化功能
"""

import cProfile
import pstats
import io
import time
import threading
import weakref
from functools import wraps
from typing import Dict, Any, Callable, Optional
from pathlib import Path
import logging

from PyQt6.QtCore import QObject, QTimer, pyqtSignal
from PyQt6.QtWidgets import QApplication


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.profiler = None
        self.stats = None
        self.is_profiling = False
    
    def start_profiling(self):
        """开始性能分析"""
        if not self.is_profiling:
            self.profiler = cProfile.Profile()
            self.profiler.enable()
            self.is_profiling = True
            logging.info("性能分析已开始")
    
    def stop_profiling(self):
        """停止性能分析"""
        if self.is_profiling:
            self.profiler.disable()
            self.is_profiling = False
            
            # 生成统计信息
            s = io.StringIO()
            ps = pstats.Stats(self.profiler, stream=s)
            ps.sort_stats('cumulative')
            ps.print_stats(20)  # 显示前20个最耗时的函数
            
            self.stats = s.getvalue()
            logging.info("性能分析已停止")
            return self.stats
    
    def get_stats(self) -> Optional[str]:
        """获取统计信息"""
        return self.stats


class MemoryMonitor(QObject):
    """内存监控器"""
    
    memory_warning = pyqtSignal(int)  # 内存使用警告信号
    
    def __init__(self, warning_threshold_mb: int = 500):
        super().__init__()
        self.warning_threshold = warning_threshold_mb * 1024 * 1024  # 转换为字节
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_memory)
        self.is_monitoring = False
    
    def start_monitoring(self, interval_ms: int = 5000):
        """开始内存监控"""
        if not self.is_monitoring:
            self.timer.start(interval_ms)
            self.is_monitoring = True
            logging.info(f"内存监控已开始，检查间隔：{interval_ms}ms")
    
    def stop_monitoring(self):
        """停止内存监控"""
        if self.is_monitoring:
            self.timer.stop()
            self.is_monitoring = False
            logging.info("内存监控已停止")
    
    def check_memory(self):
        """检查内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_usage = process.memory_info().rss
            
            if memory_usage > self.warning_threshold:
                self.memory_warning.emit(memory_usage // (1024 * 1024))  # 转换为MB
                logging.warning(f"内存使用过高：{memory_usage // (1024 * 1024)}MB")
        except ImportError:
            logging.warning("psutil未安装，无法监控内存使用")
        except Exception as e:
            logging.error(f"内存监控错误：{e}")


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: Dict[str, Any] = {}
        self.access_order = []
        self._lock = threading.Lock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self._lock:
            if key in self.cache:
                # 更新访问顺序
                self.access_order.remove(key)
                self.access_order.append(key)
                return self.cache[key]
            return None
    
    def set(self, key: str, value: Any):
        """设置缓存项"""
        with self._lock:
            if key in self.cache:
                # 更新现有项
                self.access_order.remove(key)
            elif len(self.cache) >= self.max_size:
                # 移除最久未使用的项
                oldest_key = self.access_order.pop(0)
                del self.cache[oldest_key]
            
            self.cache[key] = value
            self.access_order.append(key)
    
    def remove(self, key: str):
        """移除缓存项"""
        with self._lock:
            if key in self.cache:
                del self.cache[key]
                self.access_order.remove(key)
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.access_order.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)


def performance_timer(func: Callable) -> Callable:
    """性能计时装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        if execution_time > 0.1:  # 只记录超过100ms的操作
            logging.info(f"{func.__name__} 执行时间: {execution_time:.3f}s")
        
        return result
    return wrapper


class LazyLoader:
    """懒加载器"""
    
    def __init__(self):
        self.loaded_items = weakref.WeakSet()
        self.loading_queue = []
        self.is_loading = False
    
    def add_to_queue(self, item, load_func: Callable):
        """添加到加载队列"""
        if item not in self.loaded_items:
            self.loading_queue.append((item, load_func))
    
    def process_queue(self, max_items: int = 10):
        """处理加载队列"""
        if self.is_loading or not self.loading_queue:
            return
        
        self.is_loading = True
        processed = 0
        
        while self.loading_queue and processed < max_items:
            item, load_func = self.loading_queue.pop(0)
            
            if item not in self.loaded_items:
                try:
                    load_func()
                    self.loaded_items.add(item)
                    processed += 1
                except Exception as e:
                    logging.error(f"懒加载失败：{e}")
        
        self.is_loading = False


class UIOptimizer:
    """UI优化器"""
    
    @staticmethod
    def optimize_widget_updates(widget):
        """优化控件更新"""
        widget.setUpdatesEnabled(False)
        return widget
    
    @staticmethod
    def restore_widget_updates(widget):
        """恢复控件更新"""
        widget.setUpdatesEnabled(True)
        widget.update()
    
    @staticmethod
    def batch_update(widgets, update_func: Callable):
        """批量更新控件"""
        # 禁用更新
        for widget in widgets:
            widget.setUpdatesEnabled(False)
        
        try:
            # 执行更新
            update_func()
        finally:
            # 恢复更新
            for widget in widgets:
                widget.setUpdatesEnabled(True)
                widget.update()


# 全局实例
profiler = PerformanceProfiler()
cache_manager = CacheManager()
lazy_loader = LazyLoader()
