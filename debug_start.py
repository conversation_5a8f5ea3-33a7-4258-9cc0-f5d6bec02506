#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试启动脚本 - 用于诊断黑屏问题
"""

import sys
import os
import traceback
from pathlib import Path

def check_environment():
    """检查环境"""
    print("=" * 50)
    print("环境检查")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.path[:3]}...")
    
    # 检查PyQt6
    try:
        import PyQt6
        print(f"✅ PyQt6版本: {PyQt6.QtCore.PYQT_VERSION_STR}")
    except ImportError as e:
        print(f"❌ PyQt6导入失败: {e}")
        return False
    
    # 检查Pillow
    try:
        import PIL
        print(f"✅ Pillow版本: {PIL.__version__}")
    except ImportError as e:
        print(f"❌ Pillow导入失败: {e}")
        return False
    
    return True

def test_basic_qt():
    """测试基本Qt功能"""
    print("\n" + "=" * 50)
    print("基本Qt功能测试")
    print("=" * 50)
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt6.QtCore import Qt
        
        print("✅ PyQt6基本组件导入成功")
        
        # 创建应用
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        # 创建简单窗口
        window = QMainWindow()
        window.setWindowTitle("测试窗口")
        window.setGeometry(100, 100, 400, 300)
        
        label = QLabel("如果您看到这个窗口，说明Qt工作正常！")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("font-size: 16px; padding: 20px; background-color: white; color: black;")
        window.setCentralWidget(label)
        
        window.show()
        print("✅ 测试窗口显示成功")
        
        # 不运行事件循环，只是测试创建
        window.close()
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Qt功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_project_imports():
    """测试项目模块导入"""
    print("\n" + "=" * 50)
    print("项目模块导入测试")
    print("=" * 50)
    
    # 添加src目录到路径
    src_path = Path(__file__).parent / "src"
    sys.path.insert(0, str(src_path))
    print(f"添加src路径: {src_path}")
    
    modules_to_test = [
        "utils.config_manager",
        "models.database", 
        "ui.main_window_simple",
        "ui.main_window_backup"
    ]
    
    success_count = 0
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name} 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name} 导入失败: {e}")
            # 显示详细错误信息
            if "No module named" not in str(e):
                traceback.print_exc()
    
    return success_count == len(modules_to_test)

def start_simple_version():
    """启动简化版本"""
    print("\n" + "=" * 50)
    print("启动简化版本")
    print("=" * 50)
    
    try:
        # 添加src目录到路径
        src_path = Path(__file__).parent / "src"
        sys.path.insert(0, str(src_path))
        
        from PyQt6.QtWidgets import QApplication
        from utils.config_manager import ConfigManager
        from ui.main_window_simple import SimpleMainWindow
        
        print("正在创建应用程序...")
        app = QApplication(sys.argv)
        app.setApplicationName("简笔画素材管理-调试版")
        
        print("正在初始化配置...")
        config_manager = ConfigManager()
        
        print("正在创建主窗口...")
        main_window = SimpleMainWindow(config_manager)
        
        print("正在显示窗口...")
        main_window.show()
        
        print("✅ 简化版本启动成功！")
        print("如果窗口显示正常，请关闭窗口继续测试其他版本。")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ 简化版本启动失败: {e}")
        traceback.print_exc()
        return False

def start_backup_version():
    """启动备用版本"""
    print("\n" + "=" * 50)
    print("启动备用版本")
    print("=" * 50)
    
    try:
        # 添加src目录到路径
        src_path = Path(__file__).parent / "src"
        sys.path.insert(0, str(src_path))
        
        from PyQt6.QtWidgets import QApplication
        from utils.config_manager import ConfigManager
        from ui.main_window_backup import BackupMainWindow
        
        print("正在创建应用程序...")
        app = QApplication(sys.argv)
        app.setApplicationName("简笔画素材管理-备用版")
        
        print("正在初始化配置...")
        config_manager = ConfigManager()
        
        print("正在创建主窗口...")
        main_window = BackupMainWindow(config_manager)
        
        print("正在显示窗口...")
        main_window.show()
        
        print("✅ 备用版本启动成功！")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ 备用版本启动失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("简笔画素材管理软件 - 调试启动器")
    
    # 环境检查
    if not check_environment():
        print("\n❌ 环境检查失败，请安装必要的依赖包")
        return 1
    
    # Qt功能测试
    if not test_basic_qt():
        print("\n❌ Qt功能测试失败")
        return 1
    
    # 项目模块导入测试
    if not test_project_imports():
        print("\n❌ 项目模块导入测试失败")
        return 1
    
    print("\n" + "=" * 50)
    print("所有测试通过！开始启动程序...")
    print("=" * 50)
    
    # 询问用户选择启动版本
    print("\n请选择要启动的版本:")
    print("1. 简化版本 (推荐)")
    print("2. 备用版本")
    print("3. 退出")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            return start_simple_version()
        elif choice == "2":
            return start_backup_version()
        elif choice == "3":
            print("退出程序")
            return 0
        else:
            print("无效选择，启动简化版本...")
            return start_simple_version()
            
    except KeyboardInterrupt:
        print("\n用户中断，退出程序")
        return 0
    except Exception as e:
        print(f"\n启动失败: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
