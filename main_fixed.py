#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简笔画素材管理软件 - 修复版主程序入口
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

def main():
    """主函数"""
    try:
        # 创建应用程序实例
        app = QApplication(sys.argv)
        app.setApplicationName("简笔画素材管理")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("简笔画工作室")
        
        print("正在初始化配置管理器...")
        
        # 初始化配置管理器
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        
        print("正在创建主窗口...")
        
        # 尝试导入主窗口
        try:
            from ui.main_window_simple import SimpleMainWindow
            main_window = SimpleMainWindow(config_manager)
        except ImportError:
            print("使用备用主窗口...")
            from ui.main_window_backup import BackupMainWindow
            main_window = BackupMainWindow(config_manager)
        
        print("正在显示主窗口...")
        main_window.show()
        
        print("程序启动成功！")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 尝试启动简化版本
        try:
            print("尝试启动简化版本...")
            from test_simple import SimpleMainWindow
            app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
            window = SimpleMainWindow()
            window.show()
            sys.exit(app.exec())
        except Exception as e2:
            print(f"简化版本也启动失败: {e2}")
            return 1


if __name__ == "__main__":
    main()
