#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 使用PyInstaller创建可执行文件
"""

import os
import sys
import shutil
import subprocess
import platform
import argparse
from pathlib import Path
import time


def get_version():
    """获取版本号"""
    # 从main.py中提取版本号
    main_py = Path("main.py")
    if main_py.exists():
        with open(main_py, "r", encoding="utf-8") as f:
            for line in f:
                if "app.setApplicationVersion" in line:
                    version = line.split('"')[1]
                    return version
    
    # 默认版本号
    return "1.0.0"


def clean_build_dir():
    """清理构建目录"""
    print("清理构建目录...")
    
    # 删除dist和build目录
    for dir_name in ["dist", "build"]:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"已删除 {dir_name} 目录")
    
    # 删除spec文件
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"已删除 {spec_file}")


def install_requirements():
    """安装依赖"""
    print("安装依赖...")
    
    # 安装PyInstaller
    subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
    
    # 安装项目依赖
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)


def create_spec_file(name, version, icon_path=None, console=False):
    """创建spec文件"""
    print("创建spec文件...")
    
    # 基本命令
    cmd = [
        "pyi-makespec",
        "--name", f"{name}-{version}",
        "--onefile",
        "--windowed" if not console else "--console",
        "--add-data", "src/config;src/config",
    ]
    
    # 添加图标
    if icon_path and Path(icon_path).exists():
        cmd.extend(["--icon", icon_path])
    
    # 添加主脚本
    cmd.append("main.py")
    
    # 执行命令
    subprocess.run(cmd, check=True)
    
    return f"{name}-{version}.spec"


def customize_spec_file(spec_path):
    """自定义spec文件"""
    print("自定义spec文件...")
    
    with open(spec_path, "r", encoding="utf-8") as f:
        spec_content = f.read()
    
    # 添加额外的导入
    imports_add = """
# 添加额外的导入
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集PyQt6相关模块
qt_modules = collect_submodules('PyQt6')
"""
    
    # 修改Analysis部分
    analysis_replace = """
a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('src/config', 'src/config')],
    hiddenimports=qt_modules,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)
"""
    
    # 替换内容
    if "a = Analysis(" in spec_content:
        # 添加导入
        spec_content = imports_add + spec_content
        
        # 替换Analysis部分
        start = spec_content.find("a = Analysis(")
        end = spec_content.find(")", start) + 1
        spec_content = spec_content[:start] + analysis_replace + spec_content[end+1:]
    
    # 写回文件
    with open(spec_path, "w", encoding="utf-8") as f:
        f.write(spec_content)


def build_executable(spec_path):
    """构建可执行文件"""
    print("构建可执行文件...")
    
    # 执行PyInstaller
    subprocess.run(["pyinstaller", spec_path], check=True)


def create_installer(name, version, dist_dir):
    """创建安装程序"""
    print("创建安装程序...")
    
    # 检测操作系统
    system = platform.system()
    
    if system == "Windows":
        # 使用NSIS创建Windows安装程序
        create_windows_installer(name, version, dist_dir)
    elif system == "Darwin":
        # 创建macOS DMG
        create_macos_dmg(name, version, dist_dir)
    else:
        print(f"不支持在 {system} 上创建安装程序")


def create_windows_installer(name, version, dist_dir):
    """创建Windows安装程序"""
    try:
        # 检查NSIS是否安装
        subprocess.run(["makensis", "/VERSION"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 创建NSIS脚本
        nsis_script = f"""
; 安装程序脚本
!include "MUI2.nsh"

; 应用信息
Name "{name}"
OutFile "{name}-{version}-setup.exe"
InstallDir "$PROGRAMFILES\\{name}"
InstallDirRegKey HKCU "Software\\{name}" ""

; 界面设置
!define MUI_ABORTWARNING
!define MUI_ICON "icon.ico"

; 页面
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; 语言
!insertmacro MUI_LANGUAGE "SimpChinese"

; 安装部分
Section "安装"
  SetOutPath "$INSTDIR"
  
  ; 复制文件
  File /r "{dist_dir}\\*.*"
  
  ; 创建开始菜单快捷方式
  CreateDirectory "$SMPROGRAMS\\{name}"
  CreateShortcut "$SMPROGRAMS\\{name}\\{name}.lnk" "$INSTDIR\\{name}-{version}.exe"
  
  ; 创建卸载程序
  WriteUninstaller "$INSTDIR\\卸载.exe"
  
  ; 注册卸载信息
  WriteRegStr HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{name}" "DisplayName" "{name}"
  WriteRegStr HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{name}" "UninstallString" "$\\"$INSTDIR\\卸载.exe$\\""
  WriteRegStr HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{name}" "DisplayVersion" "{version}"
  WriteRegStr HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{name}" "Publisher" "简笔画工作室"
SectionEnd

; 卸载部分
Section "Uninstall"
  ; 删除文件
  RMDir /r "$INSTDIR"
  
  ; 删除开始菜单快捷方式
  RMDir /r "$SMPROGRAMS\\{name}"
  
  ; 删除注册表项
  DeleteRegKey HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{name}"
  DeleteRegKey HKCU "Software\\{name}"
SectionEnd
"""
        
        # 写入NSIS脚本
        nsis_path = Path("installer.nsi")
        with open(nsis_path, "w", encoding="utf-8") as f:
            f.write(nsis_script)
        
        # 运行NSIS编译器
        subprocess.run(["makensis", str(nsis_path)], check=True)
        
        # 清理
        nsis_path.unlink()
        
        print(f"Windows安装程序已创建: {name}-{version}-setup.exe")
    
    except (subprocess.SubprocessError, FileNotFoundError):
        print("未找到NSIS，跳过创建Windows安装程序")


def create_macos_dmg(name, version, dist_dir):
    """创建macOS DMG"""
    try:
        # 检查create-dmg是否安装
        subprocess.run(["create-dmg", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 创建DMG
        app_path = Path(dist_dir) / f"{name}-{version}.app"
        dmg_path = Path(f"{name}-{version}.dmg")
        
        if app_path.exists():
            subprocess.run([
                "create-dmg",
                "--volname", f"{name} {version}",
                "--window-pos", "200", "120",
                "--window-size", "800", "400",
                "--icon-size", "100",
                "--icon", f"{name}-{version}.app", "200", "190",
                "--hide-extension", f"{name}-{version}.app",
                "--app-drop-link", "600", "190",
                str(dmg_path),
                str(app_path)
            ], check=True)
            
            print(f"macOS DMG已创建: {dmg_path}")
        else:
            print(f"未找到应用程序: {app_path}")
    
    except (subprocess.SubprocessError, FileNotFoundError):
        print("未找到create-dmg，跳过创建macOS DMG")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简笔画素材管理软件打包工具")
    parser.add_argument("--clean", action="store_true", help="清理构建目录")
    parser.add_argument("--icon", help="应用图标路径")
    parser.add_argument("--console", action="store_true", help="创建控制台应用")
    parser.add_argument("--installer", action="store_true", help="创建安装程序")
    parser.add_argument("--skip-build", action="store_true", help="跳过构建步骤")
    
    args = parser.parse_args()
    
    # 应用信息
    name = "简笔画素材管理"
    version = get_version()
    print(f"打包 {name} v{version}")
    
    # 清理构建目录
    if args.clean:
        clean_build_dir()
    
    # 安装依赖
    install_requirements()
    
    if not args.skip_build:
        # 创建spec文件
        spec_path = create_spec_file(name, version, args.icon, args.console)
        
        # 自定义spec文件
        customize_spec_file(spec_path)
        
        # 构建可执行文件
        build_executable(spec_path)
    
    # 创建安装程序
    if args.installer:
        dist_dir = Path("dist")
        if dist_dir.exists():
            create_installer(name, version, dist_dir)
        else:
            print("未找到dist目录，无法创建安装程序")
    
    print("打包完成！")


if __name__ == "__main__":
    start_time = time.time()
    main()
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.2f}秒")
