#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装验证脚本 - 验证项目是否正确安装和配置
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 11:
        print("❌ 需要Python 3.11或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_dependencies():
    """检查依赖包"""
    print("\n检查依赖包...")
    
    required_packages = [
        'PyQt6',
        'Pillow',
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_project_structure():
    """检查项目结构"""
    print("\n检查项目结构...")
    
    required_dirs = [
        'src',
        'src/ui',
        'src/models',
        'src/utils',
        'tests'
    ]
    
    required_files = [
        'main.py',
        'run.py',
        'requirements.txt',
        'pyproject.toml',
        'src/ui/main_window.py',
        'src/models/database.py',
        'src/utils/config_manager.py'
    ]
    
    all_good = True
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ 目录 {dir_path} - 存在")
        else:
            print(f"❌ 目录 {dir_path} - 不存在")
            all_good = False
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ 文件 {file_path} - 存在")
        else:
            print(f"❌ 文件 {file_path} - 不存在")
            all_good = False
    
    return all_good

def check_imports():
    """检查模块导入"""
    print("\n检查模块导入...")
    
    # 添加src目录到路径
    src_path = Path(__file__).parent / "src"
    sys.path.insert(0, str(src_path))
    
    modules_to_test = [
        ('utils.config_manager', 'ConfigManager'),
        ('models.database', 'DatabaseManager'),
        ('utils.performance', 'PerformanceProfiler'),
        ('utils.animations', 'FadeAnimation'),
        ('ui.styles', 'StyleManager'),
    ]
    
    all_good = True
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name} - 导入成功")
        except ImportError as e:
            print(f"❌ {module_name}.{class_name} - 导入失败: {e}")
            all_good = False
        except AttributeError as e:
            print(f"❌ {module_name}.{class_name} - 属性错误: {e}")
            all_good = False
    
    return all_good

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        # 添加src目录到路径
        src_path = Path(__file__).parent / "src"
        sys.path.insert(0, str(src_path))
        
        # 测试配置管理器
        from utils.config_manager import ConfigManager
        config = ConfigManager()
        print("✅ 配置管理器 - 创建成功")
        
        # 测试数据库管理器
        from models.database import DatabaseManager
        db = DatabaseManager(":memory:")
        print("✅ 数据库管理器 - 创建成功")
        
        # 测试性能分析器
        from utils.performance import PerformanceProfiler
        profiler = PerformanceProfiler()
        print("✅ 性能分析器 - 创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("简笔画素材管理软件 - 安装验证")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("项目结构", check_project_structure),
        ("模块导入", check_imports),
        ("基本功能", test_basic_functionality),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}检查出错: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！项目已正确安装和配置。")
        print("\n可以运行以下命令启动应用:")
        print("  python main.py")
        print("  python run.py")
    else:
        print("❌ 部分检查未通过，请根据上述提示修复问题。")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
