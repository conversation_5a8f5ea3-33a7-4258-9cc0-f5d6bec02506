#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复脚本 - 解决黑屏问题
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                            QLineEdit, QFrame, QSplitter, QToolBar, QStatusBar)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QAction

from utils.config_manager import ConfigManager


class FixedMainWindow(QMainWindow):
    """修复版主窗口"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        
        self.init_ui()
        self.restore_window_state()
        self.show_welcome()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("简笔画素材管理 v1.0.0")
        self.setMinimumSize(1000, 700)
        
        # 设置基本样式 - 使用简单的样式避免复杂主题问题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
                color: #333333;
            }
            QWidget {
                background-color: #f5f5f5;
                color: #333333;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 12px;
            }
            QLabel {
                color: #333333;
                font-size: 13px;
            }
            QPushButton {
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                padding: 8px 16px;
                color: #333333;
                font-weight: normal;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #e8f4fd;
                border-color: #0078d4;
            }
            QPushButton:pressed {
                background-color: #d0e7f8;
            }
            QLineEdit {
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
            QTextEdit {
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                color: #333333;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 12px;
                line-height: 1.4;
            }
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
            }
            QToolBar {
                background-color: #ffffff;
                border: none;
                border-bottom: 1px solid #e0e0e0;
                spacing: 8px;
                padding: 8px;
            }
            QStatusBar {
                background-color: #f8f8f8;
                border-top: 1px solid #e0e0e0;
                color: #666666;
            }
            QSplitter::handle {
                background-color: #e0e0e0;
                width: 2px;
                height: 2px;
            }
        """)
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧分类面板
        left_panel = self.create_category_panel()
        splitter.addWidget(left_panel)
        
        # 中间文件视图
        center_panel = self.create_file_panel()
        splitter.addWidget(center_panel)
        
        # 右侧预览面板
        right_panel = self.create_preview_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([200, 500, 250])
        
        # 创建状态栏
        self.create_statusbar()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 导航按钮
        back_action = QAction("← 后退", self)
        back_action.setToolTip("后退到上一个位置")
        toolbar.addAction(back_action)
        
        forward_action = QAction("前进 →", self)
        forward_action.setToolTip("前进到下一个位置")
        toolbar.addAction(forward_action)
        
        toolbar.addSeparator()
        
        # 文件操作
        import_action = QAction("📁 导入", self)
        import_action.setToolTip("导入文件到当前分类")
        import_action.triggered.connect(self.import_files)
        toolbar.addAction(import_action)
        
        export_action = QAction("📤 导出", self)
        export_action.setToolTip("导出选中的文件")
        export_action.triggered.connect(self.export_files)
        toolbar.addAction(export_action)
        
        toolbar.addSeparator()
        
        # 搜索框
        search_label = QLabel("搜索:")
        toolbar.addWidget(search_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键词搜索...")
        self.search_edit.setMaximumWidth(200)
        self.search_edit.textChanged.connect(self.on_search)
        toolbar.addWidget(self.search_edit)
        
        toolbar.addSeparator()
        
        # 视图切换
        grid_action = QAction("⊞ 网格", self)
        grid_action.setToolTip("网格视图")
        toolbar.addAction(grid_action)
        
        list_action = QAction("☰ 列表", self)
        list_action.setToolTip("列表视图")
        toolbar.addAction(list_action)
        
        toolbar.addSeparator()
        
        # 设置和帮助
        settings_action = QAction("⚙ 设置", self)
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
        
        mini_action = QAction("🗗 小窗", self)
        mini_action.setToolTip("切换到小窗模式")
        mini_action.triggered.connect(self.toggle_mini_mode)
        toolbar.addAction(mini_action)
    
    def create_category_panel(self):
        """创建分类面板"""
        panel = QFrame()
        panel.setMaximumWidth(220)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 标题
        title = QLabel("分类管理")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title.setStyleSheet("color: #333; padding: 8px 0px;")
        layout.addWidget(title)
        
        # 分类按钮
        categories = [
            ("👤 人物", "人物相关的素材"),
            ("🏞️ 场景", "场景背景素材"),
            ("🎯 道具", "道具物品素材"),
            ("📦 其他", "其他类型素材"),
            ("🗑️ 回收站", "已删除的文件")
        ]
        
        self.category_buttons = []
        for name, tooltip in categories:
            btn = QPushButton(name)
            btn.setToolTip(tooltip)
            btn.clicked.connect(lambda checked, n=name: self.switch_category(n))
            layout.addWidget(btn)
            self.category_buttons.append(btn)
        
        layout.addStretch()
        
        # 统计信息
        stats_label = QLabel("统计信息")
        stats_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        stats_label.setStyleSheet("color: #666; padding: 8px 0px;")
        layout.addWidget(stats_label)
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(120)
        self.stats_text.setPlainText("总文件数: 0\n图片: 0\n视频: 0\n音频: 0\n其他: 0")
        layout.addWidget(self.stats_text)
        
        return panel
    
    def create_file_panel(self):
        """创建文件面板"""
        panel = QFrame()
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 路径导航
        path_layout = QHBoxLayout()
        path_label = QLabel("当前位置:")
        self.path_display = QLabel("/ 人物")
        self.path_display.setStyleSheet("color: #0078d4; font-weight: bold;")
        
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.path_display)
        path_layout.addStretch()
        
        layout.addLayout(path_layout)
        
        # 文件显示区域
        self.file_display = QTextEdit()
        self.file_display.setPlainText("""欢迎使用简笔画素材管理软件！

这是文件显示区域，这里将显示当前分类下的所有文件和文件夹。

主要功能：
• 支持图片、视频、音频等多种格式
• 拖拽文件到其他软件
• 实时搜索和筛选
• 缩略图预览
• 批量操作

请点击左侧的分类按钮来浏览不同类型的素材。

如果您看到这个界面，说明程序已经成功启动！""")
        layout.addWidget(self.file_display)
        
        return panel
    
    def create_preview_panel(self):
        """创建预览面板"""
        panel = QFrame()
        panel.setMaximumWidth(280)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 预览标题
        title = QLabel("文件预览")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title.setStyleSheet("color: #333; padding: 8px 0px;")
        layout.addWidget(title)
        
        # 预览区域
        self.preview_area = QTextEdit()
        self.preview_area.setMaximumHeight(200)
        self.preview_area.setPlainText("选择文件后，这里将显示文件的预览图像。")
        layout.addWidget(self.preview_area)
        
        # 文件信息
        info_title = QLabel("文件信息")
        info_title.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        info_title.setStyleSheet("color: #666; padding: 8px 0px;")
        layout.addWidget(info_title)
        
        self.info_display = QTextEdit()
        self.info_display.setPlainText("""文件名: 未选择
文件大小: -
文件类型: -
创建时间: -
修改时间: -
分辨率: -""")
        layout.addWidget(self.info_display)
        
        # 操作按钮
        button_layout = QVBoxLayout()
        
        buttons = [
            ("📋 复制", self.copy_file),
            ("✏️ 重命名", self.rename_file),
            ("🗑️ 删除", self.delete_file),
            ("📤 导出", self.export_current_file)
        ]
        
        for text, func in buttons:
            btn = QPushButton(text)
            btn.clicked.connect(func)
            button_layout.addWidget(btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        return panel
    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        self.statusbar.showMessage("简笔画素材管理软件已启动 - 就绪")
    
    def restore_window_state(self):
        """恢复窗口状态"""
        try:
            pos = self.config_manager.settings.get("window_position", {
                "x": 100, "y": 100, "width": 1200, "height": 800
            })
            self.setGeometry(pos["x"], pos["y"], pos["width"], pos["height"])
        except Exception:
            self.setGeometry(100, 100, 1200, 800)
    
    def show_welcome(self):
        """显示欢迎信息"""
        self.statusbar.showMessage("欢迎使用简笔画素材管理软件 v1.0.0！")
    
    # 事件处理方法
    def import_files(self):
        self.statusbar.showMessage("导入文件功能 - 开发中")
    
    def export_files(self):
        self.statusbar.showMessage("导出文件功能 - 开发中")
    
    def on_search(self, text):
        self.statusbar.showMessage(f"搜索: {text}")
    
    def show_settings(self):
        self.statusbar.showMessage("设置功能 - 开发中")
    
    def toggle_mini_mode(self):
        self.statusbar.showMessage("小窗模式 - 开发中")
    
    def switch_category(self, category):
        self.path_display.setText(f"/ {category.split()[1] if ' ' in category else category}")
        self.statusbar.showMessage(f"切换到分类: {category}")
        
        # 更新文件显示
        category_info = {
            "👤 人物": "人物分类\n\n这里显示所有人物相关的素材文件。",
            "🏞️ 场景": "场景分类\n\n这里显示所有场景背景素材文件。",
            "🎯 道具": "道具分类\n\n这里显示所有道具物品素材文件。",
            "📦 其他": "其他分类\n\n这里显示其他类型的素材文件。",
            "🗑️ 回收站": "回收站\n\n这里显示已删除的文件，30天后自动清理。"
        }
        
        info = category_info.get(category, "未知分类")
        self.file_display.setPlainText(info)
    
    def copy_file(self):
        self.statusbar.showMessage("复制文件 - 开发中")
    
    def rename_file(self):
        self.statusbar.showMessage("重命名文件 - 开发中")
    
    def delete_file(self):
        self.statusbar.showMessage("删除文件 - 开发中")
    
    def export_current_file(self):
        self.statusbar.showMessage("导出当前文件 - 开发中")
    
    def closeEvent(self, event):
        """关闭事件"""
        try:
            geometry = self.geometry()
            self.config_manager.settings["window_position"] = {
                "x": geometry.x(),
                "y": geometry.y(),
                "width": geometry.width(),
                "height": geometry.height()
            }
            self.config_manager.save_settings()
        except Exception:
            pass
        
        event.accept()


def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)
        app.setApplicationName("简笔画素材管理")
        app.setApplicationVersion("1.0.0")
        
        # 初始化配置
        config_manager = ConfigManager()
        
        # 创建主窗口
        main_window = FixedMainWindow(config_manager)
        main_window.show()
        
        print("程序启动成功！")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
