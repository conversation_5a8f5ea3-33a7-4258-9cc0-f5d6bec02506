# -*- coding: utf-8 -*-
"""
性能测试
"""

import sys
import unittest
import time
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from PyQt6.QtWidgets import QApplication
from utils.performance import PerformanceProfiler, CacheManager, LazyLoader
from utils.config_manager import ConfigManager
from models.database import DatabaseManager


class TestPerformanceProfiler(unittest.TestCase):
    """性能分析器测试"""
    
    def setUp(self):
        """设置测试"""
        self.profiler = PerformanceProfiler()
    
    def test_profiling_lifecycle(self):
        """测试性能分析生命周期"""
        # 初始状态
        self.assertFalse(self.profiler.is_profiling)
        self.assertIsNone(self.profiler.stats)
        
        # 开始分析
        self.profiler.start_profiling()
        self.assertTrue(self.profiler.is_profiling)
        
        # 执行一些操作
        time.sleep(0.1)
        sum(range(1000))
        
        # 停止分析
        stats = self.profiler.stop_profiling()
        self.assertFalse(self.profiler.is_profiling)
        self.assertIsNotNone(stats)
        self.assertIsInstance(stats, str)
    
    def test_get_stats(self):
        """测试获取统计信息"""
        self.profiler.start_profiling()
        time.sleep(0.05)
        self.profiler.stop_profiling()
        
        stats = self.profiler.get_stats()
        self.assertIsNotNone(stats)
        self.assertIn("function calls", stats)


class TestCacheManager(unittest.TestCase):
    """缓存管理器测试"""
    
    def setUp(self):
        """设置测试"""
        self.cache = CacheManager(max_size=3)
    
    def test_basic_operations(self):
        """测试基本操作"""
        # 设置和获取
        self.cache.set("key1", "value1")
        self.assertEqual(self.cache.get("key1"), "value1")
        
        # 不存在的键
        self.assertIsNone(self.cache.get("nonexistent"))
        
        # 大小
        self.assertEqual(self.cache.size(), 1)
    
    def test_lru_eviction(self):
        """测试LRU淘汰机制"""
        # 填满缓存
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.set("key3", "value3")
        self.assertEqual(self.cache.size(), 3)
        
        # 添加第四个项，应该淘汰最久未使用的
        self.cache.set("key4", "value4")
        self.assertEqual(self.cache.size(), 3)
        self.assertIsNone(self.cache.get("key1"))  # 应该被淘汰
        self.assertIsNotNone(self.cache.get("key4"))
    
    def test_access_order_update(self):
        """测试访问顺序更新"""
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.set("key3", "value3")
        
        # 访问key1，使其成为最近使用的
        self.cache.get("key1")
        
        # 添加新项，key2应该被淘汰（最久未使用）
        self.cache.set("key4", "value4")
        self.assertIsNone(self.cache.get("key2"))
        self.assertIsNotNone(self.cache.get("key1"))
    
    def test_remove_and_clear(self):
        """测试移除和清空"""
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        # 移除单个项
        self.cache.remove("key1")
        self.assertIsNone(self.cache.get("key1"))
        self.assertEqual(self.cache.size(), 1)
        
        # 清空缓存
        self.cache.clear()
        self.assertEqual(self.cache.size(), 0)
        self.assertIsNone(self.cache.get("key2"))


class TestLazyLoader(unittest.TestCase):
    """懒加载器测试"""
    
    def setUp(self):
        """设置测试"""
        self.loader = LazyLoader()
        self.load_count = 0
    
    def mock_load_func(self):
        """模拟加载函数"""
        self.load_count += 1
    
    def test_add_to_queue(self):
        """测试添加到队列"""
        item = "test_item"
        self.loader.add_to_queue(item, self.mock_load_func)
        self.assertEqual(len(self.loader.loading_queue), 1)
    
    def test_process_queue(self):
        """测试处理队列"""
        items = ["item1", "item2", "item3"]
        for item in items:
            self.loader.add_to_queue(item, self.mock_load_func)
        
        # 处理队列
        self.loader.process_queue(max_items=2)
        self.assertEqual(self.load_count, 2)
        self.assertEqual(len(self.loader.loading_queue), 1)
        
        # 处理剩余项
        self.loader.process_queue()
        self.assertEqual(self.load_count, 3)
        self.assertEqual(len(self.loader.loading_queue), 0)
    
    def test_duplicate_items(self):
        """测试重复项处理"""
        item = "duplicate_item"
        
        # 添加相同项多次
        self.loader.add_to_queue(item, self.mock_load_func)
        self.loader.add_to_queue(item, self.mock_load_func)
        self.loader.add_to_queue(item, self.mock_load_func)
        
        # 处理队列
        self.loader.process_queue()
        
        # 应该只加载一次
        self.assertEqual(self.load_count, 1)


class TestDatabasePerformance(unittest.TestCase):
    """数据库性能测试"""
    
    def setUp(self):
        """设置测试"""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = Path(self.temp_dir) / "perf_test.db"
        self.db_manager = DatabaseManager(str(self.db_path))
    
    def tearDown(self):
        """清理测试"""
        self.db_manager.close()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_bulk_insert_performance(self):
        """测试批量插入性能"""
        start_time = time.time()
        
        # 插入1000个文件记录
        for i in range(1000):
            self.db_manager.add_file(
                name=f"file_{i}.jpg",
                path=f"/test/file_{i}.jpg",
                category="人物",
                type="image"
            )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 应该在合理时间内完成（比如5秒）
        self.assertLess(execution_time, 5.0)
        print(f"批量插入1000条记录耗时: {execution_time:.3f}秒")
    
    def test_search_performance(self):
        """测试搜索性能"""
        # 先插入一些测试数据
        for i in range(100):
            self.db_manager.add_file(
                name=f"test_file_{i}.jpg",
                path=f"/test/test_file_{i}.jpg",
                category="人物" if i % 2 == 0 else "场景",
                type="image"
            )
        
        start_time = time.time()
        
        # 执行搜索
        results = self.db_manager.search_files("test_file")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 搜索应该很快完成
        self.assertLess(execution_time, 1.0)
        self.assertGreater(len(results), 0)
        print(f"搜索100条记录耗时: {execution_time:.3f}秒")


class TestUIPerformance(unittest.TestCase):
    """UI性能测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(config_dir=Path(self.temp_dir))
    
    def tearDown(self):
        """清理测试"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_window_creation_time(self):
        """测试窗口创建时间"""
        from ui.main_window import MainWindow
        
        start_time = time.time()
        main_window = MainWindow(self.config_manager)
        end_time = time.time()
        
        creation_time = end_time - start_time
        
        # 窗口创建应该在合理时间内完成
        self.assertLess(creation_time, 3.0)
        print(f"主窗口创建耗时: {creation_time:.3f}秒")
        
        main_window.close()
    
    def test_file_view_loading_time(self):
        """测试文件视图加载时间"""
        # 这里可以测试大量文件的加载性能
        pass


class TestMemoryUsage(unittest.TestCase):
    """内存使用测试"""
    
    def test_cache_memory_usage(self):
        """测试缓存内存使用"""
        cache = CacheManager(max_size=1000)
        
        # 添加大量数据
        for i in range(1000):
            cache.set(f"key_{i}", f"value_{i}" * 100)  # 每个值约600字节
        
        # 检查缓存大小
        self.assertEqual(cache.size(), 1000)
        
        # 清空缓存
        cache.clear()
        self.assertEqual(cache.size(), 0)
    
    def test_database_memory_usage(self):
        """测试数据库内存使用"""
        temp_dir = tempfile.mkdtemp()
        try:
            db_path = Path(temp_dir) / "memory_test.db"
            db_manager = DatabaseManager(str(db_path))
            
            # 添加大量数据
            for i in range(1000):
                db_manager.add_file(
                    name=f"file_{i}.jpg",
                    path=f"/test/file_{i}.jpg",
                    category="人物",
                    type="image"
                )
            
            # 检查数据库文件大小
            db_size = db_path.stat().st_size
            self.assertGreater(db_size, 0)
            print(f"数据库文件大小: {db_size / 1024:.2f} KB")
            
            db_manager.close()
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)


if __name__ == "__main__":
    # 运行性能测试
    unittest.main(verbosity=2)
