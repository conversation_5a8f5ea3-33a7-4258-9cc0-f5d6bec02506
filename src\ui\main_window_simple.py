# -*- coding: utf-8 -*-
"""
简化主窗口 - 用于解决黑屏问题
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                            QSplitter, QToolBar, QStatusBar, QPushButton,
                            QLineEdit, QLabel, QFrame, QTextEdit)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QAction, QKeySequence

from utils.config_manager import ConfigManager


class SimpleMainWindow(QMainWindow):
    """简化的主窗口类"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        
        self.init_ui()
        self.setup_basic_shortcuts()
        self.restore_window_state()
        
        # 显示欢迎信息
        self.show_welcome_message()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("简笔画素材管理")
        self.setMinimumSize(800, 600)
        
        # 设置基本样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 6px 12px;
                color: #ffffff;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4a4a4a;
                border-color: #666666;
            }
            QLineEdit {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 6px;
                color: #ffffff;
            }
            QTextEdit {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 4px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建顶部工具栏区域
        self.create_toolbar()
        
        # 创建主要内容区域
        content_layout = QHBoxLayout()
        
        # 左侧分类列表
        left_panel = self.create_left_panel()
        content_layout.addWidget(left_panel, 1)
        
        # 中间文件视图
        center_panel = self.create_center_panel()
        content_layout.addWidget(center_panel, 3)
        
        # 右侧预览面板
        right_panel = self.create_right_panel()
        content_layout.addWidget(right_panel, 1)
        
        main_layout.addLayout(content_layout)
        
        # 创建状态栏
        self.create_statusbar()
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar = QToolBar()
        self.addToolBar(self.toolbar)
        
        # 导航按钮
        back_action = QAction("← 后退", self)
        back_action.setShortcut(QKeySequence("Alt+Left"))
        self.toolbar.addAction(back_action)
        
        forward_action = QAction("前进 →", self)
        forward_action.setShortcut(QKeySequence("Alt+Right"))
        self.toolbar.addAction(forward_action)
        
        self.toolbar.addSeparator()
        
        # 导入导出按钮
        import_action = QAction("导入", self)
        import_action.triggered.connect(self.import_files)
        self.toolbar.addAction(import_action)
        
        export_action = QAction("导出", self)
        export_action.triggered.connect(self.export_files)
        self.toolbar.addAction(export_action)
        
        self.toolbar.addSeparator()
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索文件...")
        self.search_edit.setMaximumWidth(200)
        self.toolbar.addWidget(self.search_edit)
        
        self.toolbar.addSeparator()
        
        # 设置和小窗模式
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        self.toolbar.addAction(settings_action)
        
        mini_action = QAction("小窗模式", self)
        mini_action.triggered.connect(self.toggle_mini_window)
        self.toolbar.addAction(mini_action)
    
    def create_left_panel(self):
        """创建左侧分类面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setMaximumWidth(200)
        
        layout = QVBoxLayout(panel)
        
        # 分类标题
        title = QLabel("分类")
        title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        layout.addWidget(title)
        
        # 分类按钮
        categories = ["人物", "场景", "道具", "其他", "回收站"]
        for category in categories:
            btn = QPushButton(category)
            btn.clicked.connect(lambda checked, cat=category: self.switch_category(cat))
            layout.addWidget(btn)
        
        layout.addStretch()
        return panel
    
    def create_center_panel(self):
        """创建中间文件视图面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        
        layout = QVBoxLayout(panel)
        
        # 路径导航
        path_label = QLabel("当前位置: /")
        layout.addWidget(path_label)
        
        # 文件显示区域
        file_area = QTextEdit()
        file_area.setPlainText("文件显示区域\n\n这里将显示当前分类下的文件和文件夹。\n\n程序已成功启动！")
        layout.addWidget(file_area)
        
        return panel
    
    def create_right_panel(self):
        """创建右侧预览面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setMaximumWidth(250)
        
        layout = QVBoxLayout(panel)
        
        # 预览标题
        title = QLabel("预览")
        title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        layout.addWidget(title)
        
        # 预览区域
        preview_area = QTextEdit()
        preview_area.setPlainText("预览区域\n\n选中文件后，这里将显示文件的详细信息和预览。")
        preview_area.setMaximumHeight(200)
        layout.addWidget(preview_area)
        
        # 文件信息
        info_area = QTextEdit()
        info_area.setPlainText("文件信息\n\n文件名: \n文件大小: \n创建时间: \n修改时间: ")
        layout.addWidget(info_area)
        
        return panel
    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        self.statusbar.showMessage("就绪")
    
    def setup_basic_shortcuts(self):
        """设置基本快捷键"""
        # F2 重命名
        rename_action = QAction(self)
        rename_action.setShortcut(QKeySequence("F2"))
        rename_action.triggered.connect(self.rename_file)
        self.addAction(rename_action)
        
        # Ctrl+F 搜索
        search_action = QAction(self)
        search_action.setShortcut(QKeySequence("Ctrl+F"))
        search_action.triggered.connect(self.focus_search)
        self.addAction(search_action)
    
    def restore_window_state(self):
        """恢复窗口状态"""
        try:
            pos = self.config_manager.settings.get("window_position", {
                "x": 100, "y": 100, "width": 1000, "height": 700
            })
            self.setGeometry(pos["x"], pos["y"], pos["width"], pos["height"])
        except Exception as e:
            print(f"恢复窗口状态失败: {e}")
            self.setGeometry(100, 100, 1000, 700)
    
    def show_welcome_message(self):
        """显示欢迎信息"""
        self.statusbar.showMessage("欢迎使用简笔画素材管理软件！程序已成功启动。")
    
    # 槽函数
    def import_files(self):
        """导入文件"""
        self.statusbar.showMessage("导入功能开发中...")
    
    def export_files(self):
        """导出文件"""
        self.statusbar.showMessage("导出功能开发中...")
    
    def show_settings(self):
        """显示设置"""
        self.statusbar.showMessage("设置功能开发中...")
    
    def toggle_mini_window(self):
        """切换小窗模式"""
        self.statusbar.showMessage("小窗模式功能开发中...")
    
    def switch_category(self, category):
        """切换分类"""
        self.statusbar.showMessage(f"切换到分类: {category}")
    
    def rename_file(self):
        """重命名文件"""
        self.statusbar.showMessage("重命名功能开发中...")
    
    def focus_search(self):
        """聚焦搜索框"""
        self.search_edit.setFocus()
        self.search_edit.selectAll()
    
    def closeEvent(self, event):
        """关闭事件"""
        # 保存窗口状态
        try:
            geometry = self.geometry()
            self.config_manager.settings["window_position"] = {
                "x": geometry.x(),
                "y": geometry.y(),
                "width": geometry.width(),
                "height": geometry.height()
            }
            self.config_manager.save_settings()
        except Exception as e:
            print(f"保存窗口状态失败: {e}")
        
        event.accept()
