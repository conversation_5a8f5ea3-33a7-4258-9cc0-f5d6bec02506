#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试程序 - 用于诊断黑屏问题
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt6.QtCore import Qt

class SimpleMainWindow(QMainWindow):
    """简化的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("简笔画素材管理 - 测试版本")
        self.setMinimumSize(800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("简笔画素材管理软件")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #333333;
                padding: 20px;
                background-color: #f0f0f0;
                border-radius: 10px;
                margin: 20px;
            }
        """)
        layout.addWidget(label)
        
        # 添加按钮
        button = QPushButton("测试按钮")
        button.setStyleSheet("""
            QPushButton {
                font-size: 16px;
                padding: 10px 20px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button.clicked.connect(self.on_button_clicked)
        layout.addWidget(button)
        
        # 设置窗口背景色
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
            }
        """)
    
    def on_button_clicked(self):
        """按钮点击事件"""
        print("按钮被点击了！")


def main():
    """主函数"""
    try:
        # 创建应用程序实例
        app = QApplication(sys.argv)
        app.setApplicationName("简笔画素材管理测试")
        app.setApplicationVersion("1.0.0")
        
        print("正在创建主窗口...")
        
        # 创建主窗口
        main_window = SimpleMainWindow()
        main_window.show()
        
        print("主窗口已显示")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
