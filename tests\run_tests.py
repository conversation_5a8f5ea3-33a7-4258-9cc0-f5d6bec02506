#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器 - 运行所有测试并生成报告
"""

import sys
import unittest
import time
import os
from pathlib import Path
from io import StringIO

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# 导入测试模块
from test_main import *
from test_performance import *


class TestResult:
    """测试结果类"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.execution_time = 0
        self.failures = []
        self.errors = []
    
    def add_result(self, result):
        """添加测试结果"""
        self.total_tests += result.testsRun
        self.failed_tests += len(result.failures)
        self.error_tests += len(result.errors)
        self.skipped_tests += len(result.skipped) if hasattr(result, 'skipped') else 0
        self.passed_tests = self.total_tests - self.failed_tests - self.error_tests - self.skipped_tests
        
        self.failures.extend(result.failures)
        self.errors.extend(result.errors)
    
    def get_success_rate(self):
        """获取成功率"""
        if self.total_tests == 0:
            return 0
        return (self.passed_tests / self.total_tests) * 100


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.result = TestResult()
        self.verbose = True
    
    def run_test_suite(self, test_suite, suite_name):
        """运行测试套件"""
        print(f"\n{'='*60}")
        print(f"运行测试套件: {suite_name}")
        print(f"{'='*60}")
        
        # 捕获输出
        stream = StringIO()
        runner = unittest.TextTestRunner(
            stream=stream,
            verbosity=2 if self.verbose else 1
        )
        
        start_time = time.time()
        result = runner.run(test_suite)
        end_time = time.time()
        
        execution_time = end_time - start_time
        self.result.execution_time += execution_time
        self.result.add_result(result)
        
        # 显示结果
        output = stream.getvalue()
        print(output)
        
        print(f"\n{suite_name} 执行时间: {execution_time:.3f}秒")
        print(f"测试数量: {result.testsRun}")
        print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
        print(f"失败: {len(result.failures)}")
        print(f"错误: {len(result.errors)}")
        
        return result
    
    def run_all_tests(self):
        """运行所有测试"""
        print("简笔画素材管理软件 - 测试套件")
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 主要功能测试
        main_suite = unittest.TestLoader().loadTestsFromModule(sys.modules['test_main'])
        self.run_test_suite(main_suite, "主要功能测试")
        
        # 性能测试
        perf_suite = unittest.TestLoader().loadTestsFromModule(sys.modules['test_performance'])
        self.run_test_suite(perf_suite, "性能测试")
        
        # 生成总结报告
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """生成总结报告"""
        print(f"\n{'='*60}")
        print("测试总结报告")
        print(f"{'='*60}")
        
        print(f"总执行时间: {self.result.execution_time:.3f}秒")
        print(f"总测试数量: {self.result.total_tests}")
        print(f"通过: {self.result.passed_tests}")
        print(f"失败: {self.result.failed_tests}")
        print(f"错误: {self.result.error_tests}")
        print(f"跳过: {self.result.skipped_tests}")
        print(f"成功率: {self.result.get_success_rate():.1f}%")
        
        # 显示失败和错误详情
        if self.result.failures:
            print(f"\n{'='*40}")
            print("失败详情:")
            print(f"{'='*40}")
            for test, traceback in self.result.failures:
                print(f"\n失败测试: {test}")
                print(f"错误信息:\n{traceback}")
        
        if self.result.errors:
            print(f"\n{'='*40}")
            print("错误详情:")
            print(f"{'='*40}")
            for test, traceback in self.result.errors:
                print(f"\n错误测试: {test}")
                print(f"错误信息:\n{traceback}")
        
        # 保存报告到文件
        self.save_report_to_file()
        
        # 返回是否所有测试都通过
        return self.result.failed_tests == 0 and self.result.error_tests == 0
    
    def save_report_to_file(self):
        """保存报告到文件"""
        report_dir = Path(__file__).parent / "reports"
        report_dir.mkdir(exist_ok=True)
        
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        report_file = report_dir / f"test_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("简笔画素材管理软件 - 测试报告\n")
            f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'='*60}\n\n")
            
            f.write(f"总执行时间: {self.result.execution_time:.3f}秒\n")
            f.write(f"总测试数量: {self.result.total_tests}\n")
            f.write(f"通过: {self.result.passed_tests}\n")
            f.write(f"失败: {self.result.failed_tests}\n")
            f.write(f"错误: {self.result.error_tests}\n")
            f.write(f"跳过: {self.result.skipped_tests}\n")
            f.write(f"成功率: {self.result.get_success_rate():.1f}%\n\n")
            
            if self.result.failures:
                f.write("失败详情:\n")
                f.write("="*40 + "\n")
                for test, traceback in self.result.failures:
                    f.write(f"\n失败测试: {test}\n")
                    f.write(f"错误信息:\n{traceback}\n")
            
            if self.result.errors:
                f.write("\n错误详情:\n")
                f.write("="*40 + "\n")
                for test, traceback in self.result.errors:
                    f.write(f"\n错误测试: {test}\n")
                    f.write(f"错误信息:\n{traceback}\n")
        
        print(f"\n测试报告已保存到: {report_file}")


class QuickTestRunner:
    """快速测试运行器 - 只运行关键测试"""
    
    def __init__(self):
        self.test_classes = [
            TestConfigManager,
            TestDatabaseManager,
            TestCacheManager,
            TestPerformanceProfiler
        ]
    
    def run_quick_tests(self):
        """运行快速测试"""
        print("运行快速测试...")
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_class in self.test_classes:
            suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
            runner = unittest.TextTestRunner(verbosity=1)
            
            print(f"\n运行 {test_class.__name__}...")
            result = runner.run(suite)
            
            total_tests += result.testsRun
            failed_tests += len(result.failures) + len(result.errors)
            passed_tests += result.testsRun - len(result.failures) - len(result.errors)
        
        print(f"\n快速测试完成:")
        print(f"总测试: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        
        return failed_tests == 0


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简笔画素材管理软件测试运行器')
    parser.add_argument('--quick', action='store_true', help='运行快速测试')
    parser.add_argument('--performance', action='store_true', help='只运行性能测试')
    parser.add_argument('--main', action='store_true', help='只运行主要功能测试')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.quick:
        runner = QuickTestRunner()
        success = runner.run_quick_tests()
    elif args.performance:
        runner = TestRunner()
        perf_suite = unittest.TestLoader().loadTestsFromModule(sys.modules['test_performance'])
        result = runner.run_test_suite(perf_suite, "性能测试")
        success = len(result.failures) == 0 and len(result.errors) == 0
    elif args.main:
        runner = TestRunner()
        main_suite = unittest.TestLoader().loadTestsFromModule(sys.modules['test_main'])
        result = runner.run_test_suite(main_suite, "主要功能测试")
        success = len(result.failures) == 0 and len(result.errors) == 0
    else:
        runner = TestRunner()
        runner.verbose = args.verbose
        success = runner.run_all_tests()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
