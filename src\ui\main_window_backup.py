# -*- coding: utf-8 -*-
"""
备用主窗口 - 最简化版本
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QPushButton, QTextEdit, QLineEdit)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from utils.config_manager import ConfigManager


class BackupMainWindow(QMainWindow):
    """备用主窗口类 - 最简化版本"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        
        self.init_ui()
        self.show_welcome()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("简笔画素材管理 - 备用版本")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置简单的白色背景
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
            QWidget {
                background-color: white;
                color: black;
            }
            QLabel {
                color: black;
                font-size: 14px;
            }
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 8px 16px;
                color: black;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QTextEdit {
                background-color: #fafafa;
                border: 1px solid #ddd;
                border-radius: 4px;
                color: black;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QLineEdit {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 6px;
                color: black;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题
        title = QLabel("简笔画素材管理软件")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Microsoft YaHei", 20, QFont.Weight.Bold))
        title.setStyleSheet("color: #333; margin: 20px; padding: 10px;")
        main_layout.addWidget(title)
        
        # 副标题
        subtitle = QLabel("专业级素材管理解决方案")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle.setFont(QFont("Microsoft YaHei", 12))
        subtitle.setStyleSheet("color: #666; margin-bottom: 20px;")
        main_layout.addWidget(subtitle)
        
        # 功能按钮区域
        button_layout = QHBoxLayout()
        
        buttons = [
            ("人物分类", self.open_character),
            ("场景分类", self.open_scene),
            ("道具分类", self.open_props),
            ("其他分类", self.open_other),
            ("回收站", self.open_recycle)
        ]
        
        for text, func in buttons:
            btn = QPushButton(text)
            btn.clicked.connect(func)
            btn.setMinimumHeight(40)
            button_layout.addWidget(btn)
        
        main_layout.addLayout(button_layout)
        
        # 搜索区域
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入文件名进行搜索...")
        search_btn = QPushButton("搜索")
        search_btn.clicked.connect(self.search_files)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_btn)
        
        main_layout.addLayout(search_layout)
        
        # 信息显示区域
        self.info_area = QTextEdit()
        self.info_area.setReadOnly(True)
        self.info_area.setMaximumHeight(300)
        main_layout.addWidget(self.info_area)
        
        # 操作按钮区域
        action_layout = QHBoxLayout()
        
        action_buttons = [
            ("导入文件", self.import_files),
            ("导出文件", self.export_files),
            ("设置", self.show_settings),
            ("关于", self.show_about)
        ]
        
        for text, func in action_buttons:
            btn = QPushButton(text)
            btn.clicked.connect(func)
            action_layout.addWidget(btn)
        
        main_layout.addLayout(action_layout)
        
        # 状态信息
        self.status_label = QLabel("程序已启动，欢迎使用！")
        self.status_label.setStyleSheet("color: #666; font-size: 12px; padding: 10px;")
        main_layout.addWidget(self.status_label)
    
    def show_welcome(self):
        """显示欢迎信息"""
        welcome_text = """
欢迎使用简笔画素材管理软件！

这是备用版本的界面，提供基本的功能演示。

主要功能：
• 分类管理：人物、场景、道具、其他、回收站
• 文件操作：导入、导出、搜索、预览
• 拖拽支持：支持文件拖拽操作
• 小窗模式：便于与其他软件配合使用
• 自定义主题：多种主题可选

如果您看到这个界面，说明程序已经成功启动！

请点击上方的分类按钮来浏览不同类型的素材。
        """
        self.info_area.setPlainText(welcome_text)
    
    # 分类按钮事件
    def open_character(self):
        self.status_label.setText("打开人物分类")
        self.info_area.setPlainText("人物分类\n\n这里将显示所有人物相关的素材文件。\n包括：主角、路人、怪兽等子分类。")
    
    def open_scene(self):
        self.status_label.setText("打开场景分类")
        self.info_area.setPlainText("场景分类\n\n这里将显示所有场景相关的素材文件。\n包括：室内、室外等子分类。")
    
    def open_props(self):
        self.status_label.setText("打开道具分类")
        self.info_area.setPlainText("道具分类\n\n这里将显示所有道具相关的素材文件。\n包括：武器、物品、载具等子分类。")
    
    def open_other(self):
        self.status_label.setText("打开其他分类")
        self.info_area.setPlainText("其他分类\n\n这里将显示其他类型的素材文件。\n包括：BGM、音效、视频、文本等子分类。")
    
    def open_recycle(self):
        self.status_label.setText("打开回收站")
        self.info_area.setPlainText("回收站\n\n这里显示已删除的文件。\n文件将在30天后自动永久删除。")
    
    # 功能按钮事件
    def search_files(self):
        search_text = self.search_input.text()
        self.status_label.setText(f"搜索: {search_text}")
        self.info_area.setPlainText(f"搜索结果\n\n搜索关键词: {search_text}\n\n这里将显示匹配的文件列表。")
    
    def import_files(self):
        self.status_label.setText("导入文件功能")
        self.info_area.setPlainText("导入文件\n\n这里可以导入新的素材文件到指定分类中。\n支持批量导入和自动分类。")
    
    def export_files(self):
        self.status_label.setText("导出文件功能")
        self.info_area.setPlainText("导出文件\n\n这里可以将选中的文件导出到指定位置。\n支持导出到剪映草稿文件夹。")
    
    def show_settings(self):
        self.status_label.setText("设置功能")
        self.info_area.setPlainText("设置\n\n这里可以配置软件的各种选项：\n• 主题设置\n• 文件路径配置\n• 快捷键设置\n• 性能优化选项")
    
    def show_about(self):
        self.status_label.setText("关于软件")
        self.info_area.setPlainText("关于简笔画素材管理软件\n\n版本: 1.0.0\n开发者: AI Assistant\n\n这是一款专为创意工作者设计的素材管理工具。\n提供强大的分类管理、搜索、预览等功能。")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.status_label.setText("正在关闭程序...")
        event.accept()
