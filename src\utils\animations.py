# -*- coding: utf-8 -*-
"""
动画效果模块 - 提供平滑的UI动画效果
"""

from PyQt6.QtCore import (QPropertyAnimation, QEasingCurve, QRect, QSize, 
                         QPoint, QParallelAnimationGroup, QSequentialAnimationGroup,
                         pyqtSignal, QObject, QTimer)
from PyQt6.QtWidgets import QWidget, QGraphicsOpacityEffect
from PyQt6.QtGui import QColor
from typing import Optional, Callable


class AnimationManager(QObject):
    """动画管理器"""
    
    animation_finished = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.animations = []
        self.current_animation = None
    
    def add_animation(self, animation):
        """添加动画"""
        self.animations.append(animation)
        animation.finished.connect(self._on_animation_finished)
    
    def start_all(self):
        """开始所有动画"""
        for animation in self.animations:
            animation.start()
    
    def stop_all(self):
        """停止所有动画"""
        for animation in self.animations:
            animation.stop()
    
    def clear(self):
        """清除所有动画"""
        self.stop_all()
        self.animations.clear()
    
    def _on_animation_finished(self):
        """动画完成处理"""
        self.animation_finished.emit()


class FadeAnimation:
    """淡入淡出动画"""
    
    @staticmethod
    def fade_in(widget: QWidget, duration: int = 300, callback: Optional[Callable] = None):
        """淡入动画"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        
        if callback:
            animation.finished.connect(callback)
        
        animation.start()
        return animation
    
    @staticmethod
    def fade_out(widget: QWidget, duration: int = 300, callback: Optional[Callable] = None):
        """淡出动画"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(1.0)
        animation.setEndValue(0.0)
        animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        
        if callback:
            animation.finished.connect(callback)
        
        animation.start()
        return animation
    
    @staticmethod
    def fade_toggle(widget: QWidget, duration: int = 300):
        """切换淡入淡出"""
        effect = widget.graphicsEffect()
        if not effect:
            effect = QGraphicsOpacityEffect()
            widget.setGraphicsEffect(effect)
        
        current_opacity = effect.opacity()
        target_opacity = 0.0 if current_opacity > 0.5 else 1.0
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(current_opacity)
        animation.setEndValue(target_opacity)
        animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        
        animation.start()
        return animation


class SlideAnimation:
    """滑动动画"""
    
    @staticmethod
    def slide_in_from_left(widget: QWidget, duration: int = 300):
        """从左侧滑入"""
        start_pos = QPoint(-widget.width(), widget.y())
        end_pos = widget.pos()
        
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(end_pos)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        widget.move(start_pos)
        animation.start()
        return animation
    
    @staticmethod
    def slide_in_from_right(widget: QWidget, duration: int = 300):
        """从右侧滑入"""
        parent_width = widget.parent().width() if widget.parent() else 800
        start_pos = QPoint(parent_width, widget.y())
        end_pos = widget.pos()
        
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(end_pos)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        widget.move(start_pos)
        animation.start()
        return animation
    
    @staticmethod
    def slide_out_to_left(widget: QWidget, duration: int = 300):
        """向左侧滑出"""
        start_pos = widget.pos()
        end_pos = QPoint(-widget.width(), widget.y())
        
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(end_pos)
        animation.setEasingCurve(QEasingCurve.Type.InCubic)
        
        animation.start()
        return animation


class ScaleAnimation:
    """缩放动画"""
    
    @staticmethod
    def scale_in(widget: QWidget, duration: int = 300):
        """缩放进入"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        
        # 从中心点开始缩放
        current_rect = widget.geometry()
        center = current_rect.center()
        
        start_rect = QRect(center.x(), center.y(), 0, 0)
        end_rect = current_rect
        
        animation.setStartValue(start_rect)
        animation.setEndValue(end_rect)
        animation.setEasingCurve(QEasingCurve.Type.OutBack)
        
        animation.start()
        return animation
    
    @staticmethod
    def scale_out(widget: QWidget, duration: int = 300):
        """缩放退出"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        
        current_rect = widget.geometry()
        center = current_rect.center()
        
        start_rect = current_rect
        end_rect = QRect(center.x(), center.y(), 0, 0)
        
        animation.setStartValue(start_rect)
        animation.setEndValue(end_rect)
        animation.setEasingCurve(QEasingCurve.Type.InBack)
        
        animation.start()
        return animation


class BounceAnimation:
    """弹跳动画"""
    
    @staticmethod
    def bounce_in(widget: QWidget, duration: int = 600):
        """弹跳进入"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        
        current_rect = widget.geometry()
        center = current_rect.center()
        
        start_rect = QRect(center.x(), center.y(), 0, 0)
        end_rect = current_rect
        
        animation.setStartValue(start_rect)
        animation.setEndValue(end_rect)
        animation.setEasingCurve(QEasingCurve.Type.OutBounce)
        
        animation.start()
        return animation


class RotateAnimation:
    """旋转动画"""
    
    @staticmethod
    def rotate_360(widget: QWidget, duration: int = 1000):
        """360度旋转"""
        # 注意：PyQt6中旋转需要使用QGraphicsView或自定义绘制
        # 这里提供一个基础框架
        pass


class ComboAnimation:
    """组合动画"""
    
    @staticmethod
    def fade_and_slide_in(widget: QWidget, duration: int = 300):
        """淡入并滑入"""
        # 创建并行动画组
        group = QParallelAnimationGroup()
        
        # 淡入动画
        fade_effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(fade_effect)
        fade_animation = QPropertyAnimation(fade_effect, b"opacity")
        fade_animation.setDuration(duration)
        fade_animation.setStartValue(0.0)
        fade_animation.setEndValue(1.0)
        
        # 滑入动画
        start_pos = QPoint(widget.x() - 50, widget.y())
        end_pos = widget.pos()
        slide_animation = QPropertyAnimation(widget, b"pos")
        slide_animation.setDuration(duration)
        slide_animation.setStartValue(start_pos)
        slide_animation.setEndValue(end_pos)
        
        group.addAnimation(fade_animation)
        group.addAnimation(slide_animation)
        
        widget.move(start_pos)
        group.start()
        return group
    
    @staticmethod
    def scale_and_fade_in(widget: QWidget, duration: int = 400):
        """缩放并淡入"""
        group = QParallelAnimationGroup()
        
        # 淡入动画
        fade_effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(fade_effect)
        fade_animation = QPropertyAnimation(fade_effect, b"opacity")
        fade_animation.setDuration(duration)
        fade_animation.setStartValue(0.0)
        fade_animation.setEndValue(1.0)
        
        # 缩放动画
        current_rect = widget.geometry()
        center = current_rect.center()
        start_rect = QRect(center.x(), center.y(), 0, 0)
        
        scale_animation = QPropertyAnimation(widget, b"geometry")
        scale_animation.setDuration(duration)
        scale_animation.setStartValue(start_rect)
        scale_animation.setEndValue(current_rect)
        scale_animation.setEasingCurve(QEasingCurve.Type.OutBack)
        
        group.addAnimation(fade_animation)
        group.addAnimation(scale_animation)
        
        group.start()
        return group


class LoadingAnimation:
    """加载动画"""
    
    def __init__(self, widget: QWidget):
        self.widget = widget
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_loading)
        self.angle = 0
        self.is_running = False
    
    def start(self):
        """开始加载动画"""
        if not self.is_running:
            self.is_running = True
            self.timer.start(50)  # 20fps
    
    def stop(self):
        """停止加载动画"""
        if self.is_running:
            self.is_running = False
            self.timer.stop()
            self.widget.update()
    
    def _update_loading(self):
        """更新加载动画"""
        self.angle = (self.angle + 10) % 360
        self.widget.update()


# 便捷函数
def animate_widget_show(widget: QWidget, animation_type: str = "fade"):
    """显示控件动画"""
    if animation_type == "fade":
        return FadeAnimation.fade_in(widget)
    elif animation_type == "slide_left":
        return SlideAnimation.slide_in_from_left(widget)
    elif animation_type == "slide_right":
        return SlideAnimation.slide_in_from_right(widget)
    elif animation_type == "scale":
        return ScaleAnimation.scale_in(widget)
    elif animation_type == "bounce":
        return BounceAnimation.bounce_in(widget)
    elif animation_type == "combo":
        return ComboAnimation.fade_and_slide_in(widget)


def animate_widget_hide(widget: QWidget, animation_type: str = "fade"):
    """隐藏控件动画"""
    if animation_type == "fade":
        return FadeAnimation.fade_out(widget)
    elif animation_type == "slide_left":
        return SlideAnimation.slide_out_to_left(widget)
    elif animation_type == "scale":
        return ScaleAnimation.scale_out(widget)
